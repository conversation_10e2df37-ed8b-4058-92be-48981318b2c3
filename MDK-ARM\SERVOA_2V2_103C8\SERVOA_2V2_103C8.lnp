--cpu Cortex-M3
"servoa_2v2_103c8\startup_stm32f103xb.o"
"servoa_2v2_103c8\canopen.o"
"servoa_2v2_103c8\crc_16.o"
"servoa_2v2_103c8\current_loop.o"
"servoa_2v2_103c8\delay.o"
"servoa_2v2_103c8\dic.o"
"servoa_2v2_103c8\ds402.o"
"servoa_2v2_103c8\modbus.o"
"servoa_2v2_103c8\motion_control.o"
"servoa_2v2_103c8\ntc_calculate.o"
"servoa_2v2_103c8\parameter.o"
"servoa_2v2_103c8\mcpwm.o"
"servoa_2v2_103c8\position_loop.o"
"servoa_2v2_103c8\sin_table.o"
"servoa_2v2_103c8\sys.o"
"servoa_2v2_103c8\utils.o"
"servoa_2v2_103c8\velocity_loop.o"
"servoa_2v2_103c8\mt6825.o"
"servoa_2v2_103c8\main.o"
"servoa_2v2_103c8\gpio.o"
"servoa_2v2_103c8\adc.o"
"servoa_2v2_103c8\can.o"
"servoa_2v2_103c8\dma.o"
"servoa_2v2_103c8\spi.o"
"servoa_2v2_103c8\tim.o"
"servoa_2v2_103c8\usart.o"
"servoa_2v2_103c8\stm32f1xx_it.o"
"servoa_2v2_103c8\stm32f1xx_hal_msp.o"
"servoa_2v2_103c8\tamagawa.o"
"servoa_2v2_103c8\vofa_function.o"
"servoa_2v2_103c8\stm32f1xx_hal_gpio_ex.o"
"servoa_2v2_103c8\stm32f1xx_hal_adc.o"
"servoa_2v2_103c8\stm32f1xx_hal_adc_ex.o"
"servoa_2v2_103c8\stm32f1xx_hal.o"
"servoa_2v2_103c8\stm32f1xx_hal_rcc.o"
"servoa_2v2_103c8\stm32f1xx_hal_rcc_ex.o"
"servoa_2v2_103c8\stm32f1xx_hal_gpio.o"
"servoa_2v2_103c8\stm32f1xx_hal_dma.o"
"servoa_2v2_103c8\stm32f1xx_hal_cortex.o"
"servoa_2v2_103c8\stm32f1xx_hal_pwr.o"
"servoa_2v2_103c8\stm32f1xx_hal_flash.o"
"servoa_2v2_103c8\stm32f1xx_hal_flash_ex.o"
"servoa_2v2_103c8\stm32f1xx_hal_exti.o"
"servoa_2v2_103c8\stm32f1xx_hal_can.o"
"servoa_2v2_103c8\stm32f1xx_hal_spi.o"
"servoa_2v2_103c8\stm32f1xx_hal_tim.o"
"servoa_2v2_103c8\stm32f1xx_hal_tim_ex.o"
"servoa_2v2_103c8\stm32f1xx_hal_uart.o"
"servoa_2v2_103c8\system_stm32f1xx.o"
--library_type=microlib --strict --scatter "SERVOA_2V2_103C8\SERVOA_2V2_103C8.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "SERVOA_2V2_103C8.map" -o SERVOA_2V2_103C8\SERVOA_2V2_103C8.axf