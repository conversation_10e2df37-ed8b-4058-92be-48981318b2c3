Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(.text) for Reset_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.RCC_IRQHandler) for RCC_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DMA1_Channel2_IRQHandler) for DMA1_Channel2_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DMA1_Channel3_IRQHandler) for DMA1_Channel3_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) for DMA1_Channel5_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DMA1_Channel6_IRQHandler) for DMA1_Channel6_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DMA1_Channel7_IRQHandler) for DMA1_Channel7_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.ADC1_2_IRQHandler) for ADC1_2_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler) for USB_LP_CAN1_RX0_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.TIM1_CC_IRQHandler) for TIM1_CC_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.TIM4_IRQHandler) for TIM4_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f103xb.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xb.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    canopen.o(i.CAN_User_Init) refers to stm32f1xx_hal_can.o(i.HAL_CAN_Init) for HAL_CAN_Init
    canopen.o(i.CAN_User_Init) refers to main.o(i.Error_Handler) for Error_Handler
    canopen.o(i.CAN_User_Init) refers to stm32f1xx_hal_can.o(i.HAL_CAN_ConfigFilter) for HAL_CAN_ConfigFilter
    canopen.o(i.CAN_User_Init) refers to stm32f1xx_hal_can.o(i.HAL_CAN_Start) for HAL_CAN_Start
    canopen.o(i.CAN_User_Init) refers to stm32f1xx_hal_can.o(i.HAL_CAN_ActivateNotification) for HAL_CAN_ActivateNotification
    canopen.o(i.CAN_User_Init) refers to canopen.o(.bss) for .bss
    canopen.o(i.CAN_User_Init) refers to canopen.o(.data) for .data
    canopen.o(i.HAL_CAN_RxFifo0MsgPendingCallback) refers to stm32f1xx_hal_can.o(i.HAL_CAN_GetRxMessage) for HAL_CAN_GetRxMessage
    canopen.o(i.HAL_CAN_RxFifo0MsgPendingCallback) refers to canopen.o(i.Process_RPDO) for Process_RPDO
    canopen.o(i.HAL_CAN_RxFifo0MsgPendingCallback) refers to canopen.o(i.SDO_Process) for SDO_Process
    canopen.o(i.HAL_CAN_RxFifo0MsgPendingCallback) refers to canopen.o(.data) for .data
    canopen.o(i.HAL_CAN_RxFifo0MsgPendingCallback) refers to canopen.o(.bss) for .bss
    canopen.o(i.HAL_CAN_RxFifo0MsgPendingCallback) refers to modbus.o(.data) for RS485_Addr
    canopen.o(i.Process_RPDO) refers to printfa.o(i.__0printf) for __2printf
    canopen.o(i.Process_RPDO) refers to modbus.o(.data) for RS485_Addr
    canopen.o(i.Process_RPDO) refers to mcpwm.o(.data) for target_Id
    canopen.o(i.Process_RPDO) refers to mcpwm.o(.data) for target_speed
    canopen.o(i.Process_TPDO) refers to stm32f1xx_hal_can.o(i.HAL_CAN_AddTxMessage) for HAL_CAN_AddTxMessage
    canopen.o(i.Process_TPDO) refers to printfa.o(i.__0printf) for __2printf
    canopen.o(i.Process_TPDO) refers to canopen.o(.bss) for .bss
    canopen.o(i.Process_TPDO) refers to modbus.o(.data) for RS485_Addr
    canopen.o(i.Process_TPDO) refers to velocity_loop.o(.data) for real_speed_filter
    canopen.o(i.Process_TPDO) refers to canopen.o(.data) for .data
    canopen.o(i.Process_TPDO) refers to position_loop.o(.data) for pos_actual
    canopen.o(i.Process_TPDO) refers to can.o(.bss) for hcan
    canopen.o(i.SDO_Process) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    canopen.o(i.SDO_Process) refers to dic.o(i.Write_OD) for Write_OD
    canopen.o(i.SDO_Process) refers to dic.o(i.Read_OD) for Read_OD
    canopen.o(i.SDO_Process) refers to stm32f1xx_hal_can.o(i.HAL_CAN_AddTxMessage) for HAL_CAN_AddTxMessage
    canopen.o(i.SDO_Process) refers to printfa.o(i.__0printf) for __2printf
    canopen.o(i.SDO_Process) refers to canopen.o(.bss) for .bss
    canopen.o(i.SDO_Process) refers to modbus.o(.data) for RS485_Addr
    canopen.o(i.SDO_Process) refers to canopen.o(.data) for .data
    canopen.o(i.SDO_Process) refers to dic.o(.data) for Write_Access
    canopen.o(i.SDO_Process) refers to can.o(.bss) for hcan
    crc_16.o(i.Get_Crc16) refers to crc_16.o(.constdata) for .constdata
    current_loop.o(i.Current_loop) refers to mcpwm.o(i.phase_current_from_adcval) for phase_current_from_adcval
    current_loop.o(i.Current_loop) refers to sin_table.o(i.arm_cos_f32) for arm_cos_f32
    current_loop.o(i.Current_loop) refers to sin_table.o(i.arm_sin_f32) for arm_sin_f32
    current_loop.o(i.Current_loop) refers to fflti.o(.text) for __aeabi_i2f
    current_loop.o(i.Current_loop) refers to fmul.o(.text) for __aeabi_fmul
    current_loop.o(i.Current_loop) refers to fadd.o(.text) for __aeabi_fadd
    current_loop.o(i.Current_loop) refers to fdiv.o(.text) for __aeabi_fdiv
    current_loop.o(i.Current_loop) refers to ffixi.o(.text) for __aeabi_f2iz
    current_loop.o(i.Current_loop) refers to velocity_loop.o(i.Low_pass_filter_1) for Low_pass_filter_1
    current_loop.o(i.Current_loop) refers to mcpwm.o(i.queue_modulation_timings) for queue_modulation_timings
    current_loop.o(i.Current_loop) refers to mcpwm.o(.data) for phase_dir
    current_loop.o(i.Current_loop) refers to mcpwm.o(.bss) for ADCValue
    current_loop.o(i.Current_loop) refers to current_loop.o(.data) for .data
    current_loop.o(i.Current_loop) refers to mcpwm.o(.data) for Id
    current_loop.o(i.Current_loop) refers to mcpwm.o(.data) for Iq
    current_loop.o(i.Current_loop) refers to mcpwm.o(.data) for Iq_real
    current_loop.o(i.Current_loop) refers to mcpwm.o(.data) for Id_real
    current_loop.o(i.Current_loop) refers to ds402.o(.data) for motor_on
    current_loop.o(i.IIt_DC_filter) refers to current_loop.o(.data) for .data
    current_loop.o(i.IIt_filter) refers to current_loop.o(.data) for .data
    current_loop.o(i.Init_current_loop_state) refers to current_loop.o(.data) for .data
    current_loop.o(i.Init_current_loop_state) refers to mcpwm.o(.data) for Iq_demand
    delay.o(i.delay_init) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig) for HAL_SYSTICK_CLKSourceConfig
    delay.o(i.delay_init) refers to delay.o(.data) for .data
    delay.o(i.delay_ms) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    delay.o(i.delay_us) refers to delay.o(.data) for .data
    dic.o(i.Check_sum) refers to dic.o(i.Cal_sum) for Cal_sum
    dic.o(i.RS232_Read_Index) refers to dic.o(i.Index_To_RSDO) for Index_To_RSDO
    dic.o(i.RS232_Read_Index) refers to dic.o(i.Cal_sum) for Cal_sum
    dic.o(i.RS232_Write_Index) refers to dic.o(i.Index_To_WSDO) for Index_To_WSDO
    dic.o(i.RS232_Write_Index) refers to dic.o(i.Cal_sum) for Cal_sum
    dic.o(i.RS485_SDO_Process) refers to dic.o(i.Read_OD) for Read_OD
    dic.o(i.RS485_SDO_Process) refers to dic.o(i.Write_OD) for Write_OD
    dic.o(i.RS485_SDO_Process) refers to dic.o(.data) for .data
    dic.o(i.Read_OD) refers to dic.o(i.Search_OD) for Search_OD
    dic.o(i.Read_OD) refers to memcpya.o(.text) for __aeabi_memcpy
    dic.o(i.Read_OD) refers to dic.o(.constdata) for .constdata
    dic.o(i.Read_Parameter) refers to dic.o(i.Write_eerom_OD) for Write_eerom_OD
    dic.o(i.Read_Parameter) refers to dic.o(.bss) for .bss
    dic.o(i.Read_eerom_OD) refers to dic.o(i.Search_eerom_OD) for Search_eerom_OD
    dic.o(i.Read_eerom_OD) refers to memcpya.o(.text) for __aeabi_memcpy
    dic.o(i.Read_eerom_OD) refers to dic.o(.constdata) for .constdata
    dic.o(i.Save_Parameter) refers to dic.o(i.Read_eerom_OD) for Read_eerom_OD
    dic.o(i.Save_Parameter) refers to dic.o(.bss) for .bss
    dic.o(i.Search_OD) refers to dic.o(.constdata) for .constdata
    dic.o(i.Search_eerom_OD) refers to dic.o(.constdata) for .constdata
    dic.o(i.VCP_Process) refers to dic.o(i.Write_OD) for Write_OD
    dic.o(i.VCP_Process) refers to dic.o(i.Cal_sum) for Cal_sum
    dic.o(i.VCP_Process) refers to dic.o(i.Read_OD) for Read_OD
    dic.o(i.VCP_Process) refers to modbus.o(.data) for RS485_Addr
    dic.o(i.VCP_Process) refers to dic.o(.data) for .data
    dic.o(i.VCP_Service_Process) refers to dic.o(i.Check_sum) for Check_sum
    dic.o(i.VCP_Service_Process) refers to dic.o(i.VCP_Process) for VCP_Process
    dic.o(i.VCP_Service_Process) refers to dic.o(.data) for .data
    dic.o(i.VCP_Service_Process) refers to dic.o(.bss) for .bss
    dic.o(i.VCP_Service_Process) refers to modbus.o(.data) for RS485_Addr
    dic.o(i.Write_OD) refers to dic.o(i.Search_OD) for Search_OD
    dic.o(i.Write_OD) refers to memcpya.o(.text) for __aeabi_memcpy
    dic.o(i.Write_OD) refers to dic.o(.constdata) for .constdata
    dic.o(i.Write_eerom_OD) refers to dic.o(i.Search_eerom_OD) for Search_eerom_OD
    dic.o(i.Write_eerom_OD) refers to memcpya.o(.text) for __aeabi_memcpy
    dic.o(i.Write_eerom_OD) refers to dic.o(.constdata) for .constdata
    dic.o(.constdata) refers to canopen.o(.data) for Error_register
    dic.o(.constdata) refers to dic.o(i.fdummy) for fdummy
    dic.o(.constdata) refers to dic.o(.data) for OD_Num
    dic.o(.constdata) refers to mcpwm.o(.data) for store_parameter
    dic.o(.constdata) refers to mcpwm.o(.data) for Error_State
    dic.o(.constdata) refers to mcpwm.o(.data) for device_temperature
    dic.o(.constdata) refers to ds402.o(.data) for control_word
    dic.o(.constdata) refers to ds402.o(.data) for status_word
    dic.o(.constdata) refers to position_loop.o(.data) for pos_actual
    dic.o(.constdata) refers to velocity_loop.o(.data) for real_speed_filter
    dic.o(.constdata) refers to mcpwm.o(.data) for Iq
    dic.o(.constdata) refers to mcpwm.o(.data) for vbus_voltage
    dic.o(.constdata) refers to mcpwm.o(.data) for target_position
    dic.o(.constdata) refers to motion_control.o(.data) for home_offest
    dic.o(.constdata) refers to motion_control.o(.data) for profile_speed
    dic.o(.constdata) refers to motion_control.o(.data) for end_speed
    dic.o(.constdata) refers to motion_control.o(.data) for homing_method
    dic.o(.constdata) refers to motion_control.o(.data) for homing_speed
    dic.o(.constdata) refers to motion_control.o(.data) for homing_acce
    dic.o(.constdata) refers to mcpwm.o(.data) for position_demand
    dic.o(.constdata) refers to mcpwm.o(.data) for target_speed
    ds402.o(i.DS402_process) refers to current_loop.o(i.Init_current_loop_state) for Init_current_loop_state
    ds402.o(i.DS402_process) refers to velocity_loop.o(i.Init_velocity_loop_state) for Init_velocity_loop_state
    ds402.o(i.DS402_process) refers to position_loop.o(i.Init_position_loop_state) for Init_position_loop_state
    ds402.o(i.DS402_process) refers to mcpwm.o(i.start_pwm) for start_pwm
    ds402.o(i.DS402_process) refers to mcpwm.o(i.find_commutation) for find_commutation
    ds402.o(i.DS402_process) refers to delay.o(i.delay_ms) for delay_ms
    ds402.o(i.DS402_process) refers to mcpwm.o(i.stop_pwm) for stop_pwm
    ds402.o(i.DS402_process) refers to ds402.o(.data) for .data
    ds402.o(i.DS402_process) refers to mcpwm.o(.data) for Error_State
    ds402.o(i.DS402_process) refers to tim.o(.bss) for htim1
    ds402.o(i.DS402_process) refers to velocity_loop.o(.data) for real_speed_filter
    ds402.o(i.DS402_process) refers to motion_control.o(.data) for target_speed_now
    ds402.o(i.DS402_process) refers to position_loop.o(.data) for pos_actual
    ds402.o(i.DS402_process) refers to mcpwm.o(.data) for speed_demand
    ds402.o(i.DS402_process) refers to mcpwm.o(.data) for position_demand
    ds402.o(i.DS402_process) refers to mcpwm.o(.data) for drv8301_error
    ds402.o(i.DS402_process) refers to mcpwm.o(.data) for ENC_Counting_Error
    modbus.o(i.HAL_UART_RxCpltCallback) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    modbus.o(i.HAL_UART_RxCpltCallback) refers to mcpwm.o(.data) for feedback_type
    modbus.o(i.HAL_UART_RxCpltCallback) refers to tamagawa.o(.data) for Tamagawa_CRC_count
    modbus.o(i.HAL_UART_RxCpltCallback) refers to tamagawa.o(.data) for Tamagawa_First
    modbus.o(i.HAL_UART_RxCpltCallback) refers to tamagawa.o(.bss) for Tamagawa_RX_BUFF
    modbus.o(i.HAL_UART_RxCpltCallback) refers to tamagawa.o(.data) for Tamagawa_calCRC
    modbus.o(i.HAL_UART_RxCpltCallback) refers to tamagawa.o(.data) for Tamagawa_count_temp
    modbus.o(i.HAL_UART_RxCpltCallback) refers to tamagawa.o(.data) for tamagawa_angle_32
    modbus.o(i.HAL_UART_RxCpltCallback) refers to tamagawa.o(.data) for tamagawa_angle
    modbus.o(i.HAL_UART_RxCpltCallback) refers to tamagawa.o(.data) for tamagawa_angle_4
    modbus.o(i.HAL_UART_RxCpltCallback) refers to tamagawa.o(.data) for tamagawa_multi_turn
    modbus.o(i.HAL_UART_RxCpltCallback) refers to tamagawa.o(.data) for tamagawa_ENID
    modbus.o(i.HAL_UART_RxCpltCallback) refers to tamagawa.o(.data) for tamagawa_ALMC
    modbus.o(i.HAL_UART_RxCpltCallback) refers to modbus.o(.data) for .data
    modbus.o(i.HAL_UART_RxCpltCallback) refers to modbus.o(.bss) for .bss
    modbus.o(i.HAL_UART_TxCpltCallback) refers to modbus.o(i.Modbus_Solve_485_Disenable) for Modbus_Solve_485_Disenable
    modbus.o(i.HAL_UART_TxCpltCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    modbus.o(i.HAL_UART_TxCpltCallback) refers to modbus.o(.bss) for .bss
    modbus.o(i.HAL_UART_TxCpltCallback) refers to usart.o(.bss) for huart3
    modbus.o(i.HAL_UART_TxCpltCallback) refers to modbus.o(.data) for .data
    modbus.o(i.Init_Modbus_Addr_List) refers to modbus.o(.data) for .data
    modbus.o(i.Init_Modbus_Addr_List) refers to modbus.o(.bss) for .bss
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for store_parameter
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for device_temperature
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for vbus_voltage
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for Iq_real
    modbus.o(i.Init_Modbus_Addr_List) refers to velocity_loop.o(.data) for real_speed_filter
    modbus.o(i.Init_Modbus_Addr_List) refers to position_loop.o(.data) for pos_actual
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for Error_State
    modbus.o(i.Init_Modbus_Addr_List) refers to current_loop.o(.data) for Driver_IIt_Real
    modbus.o(i.Init_Modbus_Addr_List) refers to current_loop.o(.data) for Driver_IIt_Real_DC
    modbus.o(i.Init_Modbus_Addr_List) refers to ds402.o(.data) for operation_mode
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for target_Iq
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for target_speed
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for target_position
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for speed_demand
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for position_demand
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for start_calibrate_hall_phase
    modbus.o(i.Init_Modbus_Addr_List) refers to tamagawa.o(.data) for set_tamagawa_zero
    modbus.o(i.Init_Modbus_Addr_List) refers to current_loop.o(.data) for kcp
    modbus.o(i.Init_Modbus_Addr_List) refers to current_loop.o(.data) for current_in_lpf_a
    modbus.o(i.Init_Modbus_Addr_List) refers to velocity_loop.o(.data) for real_speed_filter_num
    modbus.o(i.Init_Modbus_Addr_List) refers to position_loop.o(.data) for position_in_lpf_a
    modbus.o(i.Init_Modbus_Addr_List) refers to motion_control.o(.data) for auto_reverse_p_time
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for motor_code
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.bss) for hall_phase
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for ENC_Z_Phase_B
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for vel_dir
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for motor_rated_current
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for motor_peak_current
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for motor_overload_time
    modbus.o(i.Init_Modbus_Addr_List) refers to motion_control.o(.data) for gear_factor_a
    modbus.o(i.Init_Modbus_Addr_List) refers to motion_control.o(.data) for gear_factor_b
    modbus.o(i.Init_Modbus_Addr_List) refers to motion_control.o(.data) for profile_target_position
    modbus.o(i.Init_Modbus_Addr_List) refers to motion_control.o(.data) for motion_out_lpf_a
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for over_voltage
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for under_voltage
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for chop_voltage
    modbus.o(i.Init_Modbus_Addr_List) refers to mcpwm.o(.data) for over_temperature
    modbus.o(i.Init_Modbus_Addr_List) refers to current_loop.o(.data) for Driver_IIt_Filter
    modbus.o(i.Init_Modbus_Addr_List) refers to current_loop.o(.data) for Driver_IIt_Current
    modbus.o(i.Init_Modbus_Addr_List) refers to current_loop.o(.data) for Driver_IIt_Filter_DC
    modbus.o(i.Init_Modbus_Addr_List) refers to current_loop.o(.data) for Driver_IIt_Current_DC
    modbus.o(i.Modbus_03_Solve) refers to crc_16.o(i.Get_Crc16) for Get_Crc16
    modbus.o(i.Modbus_03_Solve) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    modbus.o(i.Modbus_03_Solve) refers to modbus.o(.bss) for .bss
    modbus.o(i.Modbus_03_Solve) refers to modbus.o(.data) for .data
    modbus.o(i.Modbus_03_Solve) refers to usart.o(.bss) for huart3
    modbus.o(i.Modbus_06_Solve) refers to crc_16.o(i.Get_Crc16) for Get_Crc16
    modbus.o(i.Modbus_06_Solve) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    modbus.o(i.Modbus_06_Solve) refers to modbus.o(.bss) for .bss
    modbus.o(i.Modbus_06_Solve) refers to modbus.o(.data) for .data
    modbus.o(i.Modbus_06_Solve) refers to usart.o(.bss) for huart3
    modbus.o(i.Modbus_16_Solve) refers to crc_16.o(i.Get_Crc16) for Get_Crc16
    modbus.o(i.Modbus_16_Solve) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    modbus.o(i.Modbus_16_Solve) refers to modbus.o(.bss) for .bss
    modbus.o(i.Modbus_16_Solve) refers to modbus.o(.data) for .data
    modbus.o(i.Modbus_16_Solve) refers to usart.o(.bss) for huart3
    modbus.o(i.Modbus_Solve_485_Disenable) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    modbus.o(i.Modbus_Solve_485_Enable) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    modbus.o(i.Modbus_Solve_PutString) refers to modbus.o(i.Modbus_Solve_485_Enable) for Modbus_Solve_485_Enable
    modbus.o(i.Modbus_Solve_PutString) refers to modbus.o(i.Modbus_Solve_485_Disenable) for Modbus_Solve_485_Disenable
    modbus.o(i.Modbus_Solve_PutString) refers to modbus.o(.data) for .data
    modbus.o(i.Modbus_Solve_Service) refers to crc_16.o(i.Get_Crc16) for Get_Crc16
    modbus.o(i.Modbus_Solve_Service) refers to modbus.o(i.Modbus_Solve_485_Disenable) for Modbus_Solve_485_Disenable
    modbus.o(i.Modbus_Solve_Service) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    modbus.o(i.Modbus_Solve_Service) refers to modbus.o(i.Modbus_03_Solve) for Modbus_03_Solve
    modbus.o(i.Modbus_Solve_Service) refers to modbus.o(i.Modbus_06_Solve) for Modbus_06_Solve
    modbus.o(i.Modbus_Solve_Service) refers to modbus.o(i.Modbus_16_Solve) for Modbus_16_Solve
    modbus.o(i.Modbus_Solve_Service) refers to modbus.o(.data) for .data
    modbus.o(i.Modbus_Solve_Service) refers to modbus.o(.bss) for .bss
    modbus.o(i.Modbus_Solve_Service) refers to usart.o(.bss) for huart3
    modbus.o(i.RS232_03_Solve) refers to crc_16.o(i.Get_Crc16) for Get_Crc16
    modbus.o(i.RS232_03_Solve) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    modbus.o(i.RS232_03_Solve) refers to modbus.o(.bss) for .bss
    modbus.o(i.RS232_03_Solve) refers to modbus.o(.data) for .data
    modbus.o(i.RS232_03_Solve) refers to usart.o(.bss) for huart2
    modbus.o(i.RS232_06_Solve) refers to crc_16.o(i.Get_Crc16) for Get_Crc16
    modbus.o(i.RS232_06_Solve) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    modbus.o(i.RS232_06_Solve) refers to modbus.o(.bss) for .bss
    modbus.o(i.RS232_06_Solve) refers to modbus.o(.data) for .data
    modbus.o(i.RS232_06_Solve) refers to usart.o(.bss) for huart2
    modbus.o(i.RS232_16_Solve) refers to crc_16.o(i.Get_Crc16) for Get_Crc16
    modbus.o(i.RS232_16_Solve) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    modbus.o(i.RS232_16_Solve) refers to modbus.o(.bss) for .bss
    modbus.o(i.RS232_16_Solve) refers to modbus.o(.data) for .data
    modbus.o(i.RS232_16_Solve) refers to usart.o(.bss) for huart2
    modbus.o(i.RS232_Solve_PutString) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    modbus.o(i.RS232_Solve_PutString) refers to modbus.o(.data) for .data
    modbus.o(i.RS232_Solve_PutString) refers to usart.o(.bss) for huart2
    modbus.o(i.RS232_Solve_Service) refers to crc_16.o(i.Get_Crc16) for Get_Crc16
    modbus.o(i.RS232_Solve_Service) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    modbus.o(i.RS232_Solve_Service) refers to modbus.o(i.RS232_03_Solve) for RS232_03_Solve
    modbus.o(i.RS232_Solve_Service) refers to modbus.o(i.RS232_06_Solve) for RS232_06_Solve
    modbus.o(i.RS232_Solve_Service) refers to modbus.o(i.RS232_16_Solve) for RS232_16_Solve
    modbus.o(i.RS232_Solve_Service) refers to modbus.o(.data) for .data
    modbus.o(i.RS232_Solve_Service) refers to modbus.o(.bss) for .bss
    modbus.o(i.RS232_Solve_Service) refers to usart.o(.bss) for huart2
    modbus.o(i.RS485_Process) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    modbus.o(i.RS485_Process) refers to modbus.o(.data) for .data
    modbus.o(i.uart2_init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    modbus.o(i.uart2_init) refers to memseta.o(.text) for __aeabi_memclr4
    modbus.o(i.uart2_init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    modbus.o(i.uart2_init) refers to usart.o(.bss) for huart2
    modbus.o(i.uart2_init) refers to modbus.o(.bss) for .bss
    modbus.o(i.uart2_init) refers to modbus.o(.data) for .data
    modbus.o(i.uart3_init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    modbus.o(i.uart3_init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    modbus.o(i.uart3_init) refers to usart.o(.bss) for huart3
    modbus.o(i.uart3_init) refers to modbus.o(.bss) for .bss
    motion_control.o(i.Acce_distance_cal) refers to ldiv.o(.text) for __aeabi_ldivmod
    motion_control.o(i.Acce_distance_cal) refers to mcpwm.o(.data) for feedback_resolution
    motion_control.o(i.Auto_reserve_process) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    motion_control.o(i.Auto_reserve_process) refers to motion_control.o(.data) for .data
    motion_control.o(i.Auto_reserve_process) refers to ds402.o(.data) for motor_on
    motion_control.o(i.Auto_reserve_process) refers to mcpwm.o(.data) for Error_State
    motion_control.o(i.Auto_reserve_process) refers to mcpwm.o(.data) for target_speed
    motion_control.o(i.Auto_reserve_process) refers to mcpwm.o(.data) for target_position
    motion_control.o(i.Auto_reserve_process) refers to mcpwm.o(.data) for target_Iq
    motion_control.o(i.LED_Process) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motion_control.o(i.LED_Process) refers to mcpwm.o(.data) for led_blink_counter
    motion_control.o(i.LED_Process) refers to mcpwm.o(.data) for Error_State
    motion_control.o(i.LED_Process) refers to mcpwm.o(.data) for led_blink_period
    motion_control.o(i.LED_Process) refers to ds402.o(.data) for motor_on
    motion_control.o(i.LED_Process) refers to velocity_loop.o(.data) for real_speed_filter
    motion_control.o(i.Motion_process) refers to motion_control.o(i.Acce_distance_cal) for Acce_distance_cal
    motion_control.o(i.Motion_process) refers to ldiv.o(.text) for __aeabi_ldivmod
    motion_control.o(i.Motion_process) refers to dfltl.o(.text) for __aeabi_l2d
    motion_control.o(i.Motion_process) refers to sqrt.o(i.sqrt) for sqrt
    motion_control.o(i.Motion_process) refers to dfixl.o(.text) for __aeabi_d2lz
    motion_control.o(i.Motion_process) refers to ds402.o(.data) for operation_mode
    motion_control.o(i.Motion_process) refers to mcpwm.o(.data) for speed_demand
    motion_control.o(i.Motion_process) refers to mcpwm.o(.data) for target_Iq
    motion_control.o(i.Motion_process) refers to mcpwm.o(.data) for Iq_demand
    motion_control.o(i.Motion_process) refers to motion_control.o(.data) for .data
    motion_control.o(i.Motion_process) refers to mcpwm.o(.data) for target_speed
    motion_control.o(i.Motion_process) refers to mcpwm.o(.data) for position_demand
    motion_control.o(i.Motion_process) refers to mcpwm.o(.data) for target_position
    motion_control.o(i.Motion_process) refers to position_loop.o(.data) for pos_actual
    ntc_calculate.o(i.Get_NTC_Temperature) refers to ntc_calculate.o(.data) for .data
    parameter.o(i.Exchange_motor_code) refers to mcpwm.o(.data) for motor_code
    parameter.o(i.Exchange_motor_code) refers to mcpwm.o(.data) for feedback_type
    parameter.o(i.Exchange_motor_code) refers to mcpwm.o(.data) for motor_rated_current
    parameter.o(i.Exchange_motor_code) refers to mcpwm.o(.data) for motor_peak_current
    parameter.o(i.Init_Control_Parameter) refers to ds402.o(.data) for operation_mode
    parameter.o(i.Init_Control_Parameter) refers to mcpwm.o(.data) for Iq_demand
    parameter.o(i.Init_Control_Parameter) refers to mcpwm.o(.data) for speed_demand
    parameter.o(i.Init_Control_Parameter) refers to mcpwm.o(.data) for position_demand
    parameter.o(i.Init_Control_Parameter) refers to mcpwm.o(.data) for target_Iq
    parameter.o(i.Init_Control_Parameter) refers to mcpwm.o(.data) for target_speed
    parameter.o(i.Init_Control_Parameter) refers to mcpwm.o(.data) for target_position
    parameter.o(i.Init_Control_Parameter) refers to current_loop.o(.data) for kcp
    parameter.o(i.Init_Control_Parameter) refers to current_loop.o(.data) for current_in_lpf_a
    parameter.o(i.Init_Control_Parameter) refers to velocity_loop.o(.data) for Ilim
    parameter.o(i.Init_Control_Parameter) refers to position_loop.o(.data) for vel_lim
    parameter.o(i.Init_Control_Parameter) refers to position_loop.o(.data) for position_in_lpf_a
    parameter.o(i.Init_Control_Parameter) refers to motion_control.o(.data) for profile_target_position
    parameter.o(i.Init_Control_Parameter) refers to motion_control.o(.data) for profile_speed
    parameter.o(i.Init_Control_Parameter) refers to motion_control.o(.data) for motion_out_lpf_a
    parameter.o(i.Init_Driver_State) refers to ds402.o(.data) for control_word
    parameter.o(i.Init_Driver_State) refers to mcpwm.o(.data) for target_position
    parameter.o(i.Init_Driver_State) refers to motion_control.o(.data) for profile_target_position
    parameter.o(i.Init_Driver_State) refers to motion_control.o(.data) for profile_target_position_b
    parameter.o(i.Init_Motor_Parameter) refers to mcpwm.o(.data) for commutation_current
    parameter.o(i.Init_Motor_Parameter) refers to mcpwm.o(.data) for motor_code
    parameter.o(i.Init_Motor_Parameter) refers to mcpwm.o(.data) for vel_dir
    parameter.o(i.Init_System_Parameter) refers to modbus.o(.data) for software_version
    parameter.o(i.Init_System_Parameter) refers to mcpwm.o(.data) for over_voltage
    parameter.o(i.Init_System_Parameter) refers to mcpwm.o(.data) for under_voltage
    parameter.o(i.Init_System_Parameter) refers to mcpwm.o(.data) for chop_voltage
    parameter.o(i.Init_System_Parameter) refers to mcpwm.o(.data) for over_temperature
    parameter.o(i.Init_System_Parameter) refers to current_loop.o(.data) for Driver_IIt_Filter
    parameter.o(i.Init_System_Parameter) refers to current_loop.o(.data) for Driver_IIt_Current
    parameter.o(i.Init_System_Parameter) refers to current_loop.o(.data) for Driver_IIt_Filter_DC
    parameter.o(i.Init_System_Parameter) refers to current_loop.o(.data) for Driver_IIt_Current_DC
    parameter.o(i.MemReadHalfWord) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    parameter.o(i.MemReadHalfWord) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    parameter.o(i.MemReadHalfWord) refers to parameter.o(.data) for .data
    parameter.o(i.MemReadModbus) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    parameter.o(i.MemReadModbus) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    parameter.o(i.MemReadModbus) refers to parameter.o(.data) for .data
    parameter.o(i.MemReadModbus) refers to modbus.o(.bss) for Modbus_Output_Reg
    parameter.o(i.MemWriteHalfWord) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    parameter.o(i.MemWriteHalfWord) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) for HAL_FLASHEx_Erase
    parameter.o(i.MemWriteHalfWord) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) for HAL_FLASH_Program
    parameter.o(i.MemWriteHalfWord) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    parameter.o(i.MemWriteHalfWord) refers to parameter.o(.data) for .data
    parameter.o(i.MemWriteModbus) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    parameter.o(i.MemWriteModbus) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) for HAL_FLASHEx_Erase
    parameter.o(i.MemWriteModbus) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) for HAL_FLASH_Program
    parameter.o(i.MemWriteModbus) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    parameter.o(i.MemWriteModbus) refers to parameter.o(.data) for .data
    parameter.o(i.MemWriteModbus) refers to modbus.o(.bss) for Modbus_Output_Reg
    parameter.o(i.Process_Store_parameter) refers to delay.o(i.delay_ms) for delay_ms
    parameter.o(i.Process_Store_parameter) refers to parameter.o(i.Init_Control_Parameter) for Init_Control_Parameter
    parameter.o(i.Process_Store_parameter) refers to parameter.o(i.Init_Motor_Parameter) for Init_Motor_Parameter
    parameter.o(i.Process_Store_parameter) refers to parameter.o(i.Init_System_Parameter) for Init_System_Parameter
    parameter.o(i.Process_Store_parameter) refers to parameter.o(i.MemWriteModbus) for MemWriteModbus
    parameter.o(i.Process_Store_parameter) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    parameter.o(i.Process_Store_parameter) refers to mcpwm.o(.data) for store_parameter
    parameter.o(i.Process_Store_parameter) refers to ds402.o(.data) for control_word
    mcpwm.o(i.Calibrate_ADC_Offset) refers to delay.o(i.delay_ms) for delay_ms
    mcpwm.o(i.Calibrate_ADC_Offset) refers to stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) for HAL_ADCEx_InjectedStart
    mcpwm.o(i.Calibrate_ADC_Offset) refers to tim.o(i.OC4_PWM_Override) for OC4_PWM_Override
    mcpwm.o(i.Calibrate_ADC_Offset) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) for HAL_TIM_PWM_Start_IT
    mcpwm.o(i.Calibrate_ADC_Offset) refers to adc.o(.bss) for hadc1
    mcpwm.o(i.Calibrate_ADC_Offset) refers to tim.o(.bss) for htim1
    mcpwm.o(i.Calibrate_ADC_Offset) refers to mcpwm.o(.bss) for .bss
    mcpwm.o(i.Calibrate_ADC_Offset) refers to mcpwm.o(.data) for .data
    mcpwm.o(i.find_commutation) refers to mcpwm.o(i.start_pwm) for start_pwm
    mcpwm.o(i.find_commutation) refers to mcpwm.o(i.get_electric_phase) for get_electric_phase
    mcpwm.o(i.find_commutation) refers to mcpwm.o(i.stop_pwm) for stop_pwm
    mcpwm.o(i.find_commutation) refers to mcpwm.o(.data) for .data
    mcpwm.o(i.find_commutation) refers to tamagawa.o(.data) for tamagawa_angle
    mcpwm.o(i.find_commutation) refers to tim.o(.bss) for htim1
    mcpwm.o(i.find_commutation) refers to ds402.o(.data) for motor_on
    mcpwm.o(i.find_commutation) refers to mcpwm.o(.bss) for .bss
    mcpwm.o(i.get_electric_phase) refers to delay.o(i.delay_ms) for delay_ms
    mcpwm.o(i.get_electric_phase) refers to mcpwm.o(.data) for .data
    mcpwm.o(i.init_motor_control) refers to delay.o(i.delay_ms) for delay_ms
    mcpwm.o(i.init_motor_control) refers to mcpwm.o(i.Calibrate_ADC_Offset) for Calibrate_ADC_Offset
    mcpwm.o(i.init_motor_control) refers to mcpwm.o(i.start_adc) for start_adc
    mcpwm.o(i.init_motor_control) refers to mcpwm.o(.data) for .data
    mcpwm.o(i.init_motor_control) refers to tamagawa.o(.data) for tamagawa_angle
    mcpwm.o(i.init_motor_control) refers to tamagawa.o(.data) for tamagawa_angle_b
    mcpwm.o(i.init_motor_control) refers to position_loop.o(.data) for pos_offest
    mcpwm.o(i.init_motor_control) refers to tamagawa.o(.data) for Tamagawa_First
    mcpwm.o(i.phase_current_from_adcval) refers to mcpwm.o(.data) for .data
    mcpwm.o(i.queue_modulation_timings) refers to utils.o(i.SVM) for SVM
    mcpwm.o(i.queue_modulation_timings) refers to mcpwm.o(.data) for .data
    mcpwm.o(i.safe_assert) refers to tim.o(.bss) for htim1
    mcpwm.o(i.start_adc) refers to delay.o(i.delay_ms) for delay_ms
    mcpwm.o(i.start_adc) refers to stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) for HAL_ADCEx_InjectedStart
    mcpwm.o(i.start_adc) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) for HAL_TIM_PWM_Start_IT
    mcpwm.o(i.start_adc) refers to adc.o(.bss) for hadc1
    mcpwm.o(i.start_adc) refers to tim.o(.bss) for htim1
    mcpwm.o(i.start_pwm) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    mcpwm.o(i.start_pwm) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) for HAL_TIMEx_PWMN_Start
    mcpwm.o(i.stop_pwm) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop) for HAL_TIM_PWM_Stop
    mcpwm.o(i.stop_pwm) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) for HAL_TIMEx_PWMN_Stop
    mcpwm.o(i.update_motor) refers to mcpwm.o(.data) for .data
    mcpwm.o(i.update_motor) refers to tamagawa.o(.data) for tamagawa_angle_b
    mcpwm.o(i.update_motor) refers to tamagawa.o(.data) for tamagawa_angle
    mcpwm.o(i.update_motor) refers to ds402.o(.data) for operation_mode
    mcpwm.o(.data) refers to tim.o(.bss) for htim1
    position_loop.o(i.Check_DCBus) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    position_loop.o(i.Check_DCBus) refers to mcpwm.o(.data) for vbus_voltage
    position_loop.o(i.Check_DCBus) refers to mcpwm.o(.data) for over_voltage
    position_loop.o(i.Check_DCBus) refers to mcpwm.o(.data) for Error_State
    position_loop.o(i.Check_DCBus) refers to mcpwm.o(.data) for under_voltage
    position_loop.o(i.Check_DCBus) refers to mcpwm.o(.data) for chop_voltage
    position_loop.o(i.Check_IIt) refers to current_loop.o(i.IIt_filter) for IIt_filter
    position_loop.o(i.Check_IIt) refers to current_loop.o(i.IIt_DC_filter) for IIt_DC_filter
    position_loop.o(i.Check_IIt) refers to current_loop.o(.data) for Driver_IIt_Real
    position_loop.o(i.Check_IIt) refers to mcpwm.o(.data) for Iq_real
    position_loop.o(i.Check_IIt) refers to current_loop.o(.data) for Driver_IIt_Filter
    position_loop.o(i.Check_IIt) refers to current_loop.o(.data) for Driver_IIt_Current
    position_loop.o(i.Check_IIt) refers to mcpwm.o(.data) for Error_State
    position_loop.o(i.Check_IIt) refers to current_loop.o(.data) for Driver_IIt_Real_DC
    position_loop.o(i.Check_IIt) refers to current_loop.o(.data) for Driver_IIt_Filter_DC
    position_loop.o(i.Check_IIt) refers to current_loop.o(.data) for Driver_IIt_Current_DC
    position_loop.o(i.Check_Temperature) refers to mcpwm.o(.data) for device_temperature
    position_loop.o(i.Check_Temperature) refers to mcpwm.o(.data) for over_temperature
    position_loop.o(i.Check_Temperature) refers to mcpwm.o(.data) for Error_State
    position_loop.o(i.Error_process) refers to mcpwm.o(.data) for Error_State
    position_loop.o(i.Error_process) refers to ds402.o(.data) for status_word
    position_loop.o(i.Init_position_loop_state) refers to position_loop.o(.data) for .data
    position_loop.o(i.Position_Loop) refers to fflti.o(.text) for __aeabi_i2f
    position_loop.o(i.Position_Loop) refers to fmul.o(.text) for __aeabi_fmul
    position_loop.o(i.Position_Loop) refers to fadd.o(.text) for __aeabi_fadd
    position_loop.o(i.Position_Loop) refers to ffixi.o(.text) for __aeabi_f2iz
    position_loop.o(i.Position_Loop) refers to velocity_loop.o(i.Low_pass_filter_1) for Low_pass_filter_1
    position_loop.o(i.Position_Loop) refers to position_loop.o(i.Check_DCBus) for Check_DCBus
    position_loop.o(i.Position_Loop) refers to position_loop.o(i.Check_Temperature) for Check_Temperature
    position_loop.o(i.Position_Loop) refers to position_loop.o(i.Check_IIt) for Check_IIt
    position_loop.o(i.Position_Loop) refers to ds402.o(.data) for operation_mode
    position_loop.o(i.Position_Loop) refers to position_loop.o(.data) for .data
    position_loop.o(i.Position_Loop) refers to mcpwm.o(.data) for feedback_type
    position_loop.o(i.Position_Loop) refers to tamagawa.o(.data) for tamagawa_angle
    position_loop.o(i.Position_Loop) refers to tamagawa.o(.data) for tamagawa_multi_turn
    position_loop.o(i.Position_Loop) refers to mcpwm.o(.data) for speed_demand
    sin_table.o(i.arm_cos_f32) refers to sin_table.o(.data) for .data
    sin_table.o(i.arm_cos_f32) refers to sin_table.o(.constdata) for .constdata
    sin_table.o(i.arm_sin_f32) refers to sin_table.o(.data) for .data
    sin_table.o(i.arm_sin_f32) refers to sin_table.o(.constdata) for .constdata
    velocity_loop.o(i.Init_velocity_loop_state) refers to velocity_loop.o(.data) for .data
    velocity_loop.o(i.Init_velocity_loop_state) refers to mcpwm.o(.data) for speed_demand
    velocity_loop.o(i.Low_pass_filter) refers to velocity_loop.o(.data) for .data
    velocity_loop.o(i.Update_Speed) refers to ds402.o(.data) for operation_mode
    velocity_loop.o(i.Update_Speed) refers to velocity_loop.o(.data) for .data
    velocity_loop.o(i.Update_Speed) refers to mcpwm.o(.data) for feedback_resolution
    velocity_loop.o(i.Update_Speed) refers to mcpwm.o(.data) for speed_demand
    velocity_loop.o(i.Velocity_loop) refers to velocity_loop.o(i.Update_Speed) for Update_Speed
    velocity_loop.o(i.Velocity_loop) refers to fflti.o(.text) for __aeabi_i2f
    velocity_loop.o(i.Velocity_loop) refers to fmul.o(.text) for __aeabi_fmul
    velocity_loop.o(i.Velocity_loop) refers to fadd.o(.text) for __aeabi_fadd
    velocity_loop.o(i.Velocity_loop) refers to ffixi.o(.text) for __aeabi_f2iz
    velocity_loop.o(i.Velocity_loop) refers to ds402.o(.data) for motor_on
    velocity_loop.o(i.Velocity_loop) refers to mcpwm.o(.data) for commutation_founded
    velocity_loop.o(i.Velocity_loop) refers to velocity_loop.o(.data) for .data
    velocity_loop.o(i.Velocity_loop) refers to mcpwm.o(.data) for vel_dir
    mt6825.o(i.ReadValue) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    mt6825.o(i.ReadValue) refers to mt6825.o(i.SPIx_ReadWriteByte) for SPIx_ReadWriteByte
    mt6825.o(i.ReadValue) refers to mt6825.o(.data) for .data
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to adc.o(i.MX_ADC2_Init) for MX_ADC2_Init
    main.o(i.main) refers to can.o(i.MX_CAN_Init) for MX_CAN_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to spi.o(i.MX_SPI1_Init) for MX_SPI1_Init
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to modbus.o(i.Init_Modbus_Addr_List) for Init_Modbus_Addr_List
    main.o(i.main) refers to parameter.o(i.MemReadModbus) for MemReadModbus
    main.o(i.main) refers to parameter.o(i.Exchange_motor_code) for Exchange_motor_code
    main.o(i.main) refers to modbus.o(i.uart2_init) for uart2_init
    main.o(i.main) refers to modbus.o(i.uart3_init) for uart3_init
    main.o(i.main) refers to tamagawa.o(i.uart1_init) for uart1_init
    main.o(i.main) refers to canopen.o(i.CAN_User_Init) for CAN_User_Init
    main.o(i.main) refers to parameter.o(i.Init_Driver_State) for Init_Driver_State
    main.o(i.main) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    main.o(i.main) refers to mcpwm.o(i.init_motor_control) for init_motor_control
    main.o(i.main) refers to parameter.o(i.Init_System_Parameter) for Init_System_Parameter
    main.o(i.main) refers to parameter.o(i.Init_Motor_Parameter) for Init_Motor_Parameter
    main.o(i.main) refers to parameter.o(i.Init_Control_Parameter) for Init_Control_Parameter
    main.o(i.main) refers to vofa_function.o(i.vofaJustFloatInit) for vofaJustFloatInit
    main.o(i.main) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.main) refers to ds402.o(i.DS402_process) for DS402_process
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to spi.o(.bss) for hspi1
    main.o(i.main) refers to modbus.o(.data) for RS232_Baudrate
    main.o(i.main) refers to modbus.o(.data) for RS485_Baudrate
    main.o(i.main) refers to tamagawa.o(.data) for Tamagawa_Baudrate
    main.o(i.main) refers to can.o(.bss) for hcan
    main.o(i.main) refers to tim.o(.bss) for htim4
    main.o(i.main) refers to mcpwm.o(.data) for motor_rated_current
    main.o(i.main) refers to current_loop.o(.data) for Driver_IIt_Current
    main.o(i.main) refers to mcpwm.o(.data) for store_parameter
    main.o(i.main) refers to ds402.o(.data) for operation_mode
    main.o(i.main) refers to mcpwm.o(.data) for commutation_founded
    main.o(i.main) refers to mcpwm.o(.data) for target_speed
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    adc.o(i.HAL_ADC_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    adc.o(i.MX_ADC1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.MX_ADC1_Init) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) for HAL_ADCEx_InjectedConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for .bss
    adc.o(i.MX_ADC2_Init) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.MX_ADC2_Init) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC2_Init) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC2_Init) refers to stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) for HAL_ADCEx_InjectedConfigChannel
    adc.o(i.MX_ADC2_Init) refers to adc.o(.bss) for .bss
    can.o(i.HAL_CAN_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    can.o(i.HAL_CAN_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    can.o(i.HAL_CAN_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    can.o(i.HAL_CAN_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    can.o(i.HAL_CAN_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    can.o(i.MX_CAN_Init) refers to stm32f1xx_hal_can.o(i.HAL_CAN_Init) for HAL_CAN_Init
    can.o(i.MX_CAN_Init) refers to main.o(i.Error_Handler) for Error_Handler
    can.o(i.MX_CAN_Init) refers to can.o(.bss) for .bss
    dma.o(i.MX_DMA_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    spi.o(i.HAL_SPI_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    spi.o(i.HAL_SPI_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    spi.o(i.MX_SPI1_Init) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.MX_SPI1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    spi.o(i.MX_SPI1_Init) refers to spi.o(.bss) for .bss
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) for HAL_TIM_OC_Init
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) for HAL_TIM_OC_ConfigChannel
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM1_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM4_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM4_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for .bss
    tim.o(i.OC4_PWM_Override) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) for HAL_TIM_OC_ConfigChannel
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART3_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for .bss
    stm32f1xx_it.o(i.ADC1_2_IRQHandler) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_IRQHandler) for HAL_ADC_IRQHandler
    stm32f1xx_it.o(i.ADC1_2_IRQHandler) refers to adc.o(.bss) for hadc1
    stm32f1xx_it.o(i.DMA1_Channel2_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(i.DMA1_Channel2_IRQHandler) refers to usart.o(.bss) for hdma_usart3_tx
    stm32f1xx_it.o(i.DMA1_Channel3_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(i.DMA1_Channel3_IRQHandler) refers to usart.o(.bss) for hdma_usart3_rx
    stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f1xx_it.o(i.DMA1_Channel6_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(i.DMA1_Channel6_IRQHandler) refers to usart.o(.bss) for hdma_usart2_rx
    stm32f1xx_it.o(i.DMA1_Channel7_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(i.DMA1_Channel7_IRQHandler) refers to usart.o(.bss) for hdma_usart2_tx
    stm32f1xx_it.o(i.SysTick_Handler) refers to motion_control.o(i.LED_Process) for LED_Process
    stm32f1xx_it.o(i.SysTick_Handler) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue) for HAL_ADCEx_InjectedGetValue
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_GetValue) for HAL_ADC_GetValue
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to ntc_calculate.o(i.Get_NTC_Temperature) for Get_NTC_Temperature
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to mcpwm.o(i.update_motor) for update_motor
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to current_loop.o(i.Current_loop) for Current_loop
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to tamagawa.o(i.Tamagawa_Read_Cmd) for Tamagawa_Read_Cmd
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to mt6825.o(i.ReadValue) for ReadValue
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to velocity_loop.o(i.Velocity_loop) for Velocity_loop
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to position_loop.o(i.Position_Loop) for Position_Loop
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to adc.o(.bss) for hadc1
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to mcpwm.o(.bss) for ADCValue
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to mcpwm.o(.data) for vbus_voltage
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to ntc_calculate.o(.data) for NTC_R_Value
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to mcpwm.o(.data) for device_temperature
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to mcpwm.o(.data) for Driver_Ready
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to mcpwm.o(.data) for loop_counter_c
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to mcpwm.o(.data) for loop_counter_v
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to mcpwm.o(.data) for loop_counter_p
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to mcpwm.o(.data) for current_loop_ready
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to mcpwm.o(.data) for Id_demand
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to tamagawa.o(.data) for set_tamagawa_zero
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to tamagawa.o(.bss) for Tamagawa_TX_BUFF
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to tamagawa.o(.data) for Tamagawa_First
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to tamagawa.o(.data) for Tamagawa_count_temp
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to tamagawa.o(.data) for Tamagawa_lost
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to tamagawa.o(.data) for set_tamagawa_zero_count
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to tamagawa.o(.data) for tamagawa_angle
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to mcpwm.o(.data) for velocity_loop_ready
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to mcpwm.o(.data) for speed_demand
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to mcpwm.o(.data) for position_loop_ready
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to mcpwm.o(.data) for position_demand
    stm32f1xx_it.o(i.TIM1_CC_IRQHandler) refers to tim.o(.bss) for htim1
    stm32f1xx_it.o(i.TIM4_IRQHandler) refers to canopen.o(i.Process_TPDO) for Process_TPDO
    stm32f1xx_it.o(i.TIM4_IRQHandler) refers to motion_control.o(i.Motion_process) for Motion_process
    stm32f1xx_it.o(i.TIM4_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f1xx_it.o(i.TIM4_IRQHandler) refers to canopen.o(.data) for tim_count
    stm32f1xx_it.o(i.TIM4_IRQHandler) refers to canopen.o(.data) for TPDO_Period
    stm32f1xx_it.o(i.TIM4_IRQHandler) refers to mcpwm.o(.data) for position_loop_ready
    stm32f1xx_it.o(i.TIM4_IRQHandler) refers to ds402.o(.data) for motor_on
    stm32f1xx_it.o(i.TIM4_IRQHandler) refers to tim.o(.bss) for htim4
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f1xx_it.o(i.USART2_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART2_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    stm32f1xx_it.o(i.USART2_IRQHandler) refers to vofa_function.o(i.uartCMDRecv) for uartCMDRecv
    stm32f1xx_it.o(i.USART2_IRQHandler) refers to vofa_function.o(i.vofaCommandParse) for vofaCommandParse
    stm32f1xx_it.o(i.USART2_IRQHandler) refers to memseta.o(.text) for __aeabi_memclr
    stm32f1xx_it.o(i.USART2_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    stm32f1xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f1xx_it.o(i.USART2_IRQHandler) refers to modbus.o(.data) for RS232_RX_CNT
    stm32f1xx_it.o(i.USART2_IRQHandler) refers to vofa_function.o(.bss) for vofaCommandData
    stm32f1xx_it.o(i.USART2_IRQHandler) refers to modbus.o(.bss) for RS232_RX_BUFF
    stm32f1xx_it.o(i.USART3_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART3_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    stm32f1xx_it.o(i.USART3_IRQHandler) refers to modbus.o(i.Modbus_Solve_485_Enable) for Modbus_Solve_485_Enable
    stm32f1xx_it.o(i.USART3_IRQHandler) refers to modbus.o(i.Modbus_Solve_Service) for Modbus_Solve_Service
    stm32f1xx_it.o(i.USART3_IRQHandler) refers to memseta.o(.text) for __aeabi_memclr
    stm32f1xx_it.o(i.USART3_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    stm32f1xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    stm32f1xx_it.o(i.USART3_IRQHandler) refers to stm32f1xx_it.o(.data) for .data
    stm32f1xx_it.o(i.USART3_IRQHandler) refers to modbus.o(.data) for RS485_RX_CNT
    stm32f1xx_it.o(i.USART3_IRQHandler) refers to modbus.o(.bss) for RS485_RX_BUFF
    stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) for HAL_CAN_IRQHandler
    stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler) refers to can.o(.bss) for hcan
    stm32f1xx_hal_msp.o(i.HAL_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal_msp.o(i.HAL_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal_msp.o(i.HAL_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tamagawa.o(i.Tamagawa_Read_Cmd) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    tamagawa.o(i.Tamagawa_Read_Cmd) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    tamagawa.o(i.Tamagawa_Read_Cmd) refers to tamagawa.o(.data) for .data
    tamagawa.o(i.Tamagawa_Read_Cmd) refers to mcpwm.o(.data) for feedback_type
    tamagawa.o(i.Tamagawa_Read_Cmd) refers to tamagawa.o(.bss) for .bss
    tamagawa.o(i.Tamagawa_Read_Cmd) refers to usart.o(.bss) for huart1
    tamagawa.o(i.uart1_init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    tamagawa.o(i.uart1_init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    tamagawa.o(i.uart1_init) refers to usart.o(.bss) for huart1
    tamagawa.o(i.uart1_init) refers to tamagawa.o(.data) for .data
    vofa_function.o(i.fputc) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    vofa_function.o(i.fputc) refers to usart.o(.bss) for huart2
    vofa_function.o(i.uartCMDRecv) refers to memseta.o(.text) for __aeabi_memclr
    vofa_function.o(i.uartCMDRecv) refers to vofa_function.o(.data) for .data
    vofa_function.o(i.uartCMDRecv) refers to vofa_function.o(.bss) for .bss
    vofa_function.o(i.uartSendByte) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    vofa_function.o(i.uartSendByte) refers to usart.o(.bss) for huart2
    vofa_function.o(i.uartSendData) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    vofa_function.o(i.uartSendData) refers to usart.o(.bss) for huart2
    vofa_function.o(i.vofaCommandParse) refers to memseta.o(.text) for __aeabi_memclr
    vofa_function.o(i.vofaCommandParse) refers to atof.o(i.atof) for atof
    vofa_function.o(i.vofaCommandParse) refers to d2f.o(.text) for __aeabi_d2f
    vofa_function.o(i.vofaCommandParse) refers to ffixi.o(.text) for __aeabi_f2iz
    vofa_function.o(i.vofaCommandParse) refers to vofa_function.o(.bss) for .bss
    vofa_function.o(i.vofaCommandParse) refers to ds402.o(.data) for control_word
    vofa_function.o(i.vofaCommandParse) refers to current_loop.o(.data) for kcp
    vofa_function.o(i.vofaCommandParse) refers to velocity_loop.o(.data) for kvp
    vofa_function.o(i.vofaCommandParse) refers to position_loop.o(.data) for kpp
    vofa_function.o(i.vofaCommandParse) refers to mcpwm.o(.data) for Id_demand
    vofa_function.o(i.vofaCommandParse) refers to mcpwm.o(.data) for target_speed
    vofa_function.o(i.vofaCommandParse) refers to mcpwm.o(.data) for target_position
    vofa_function.o(i.vofaCommandParse) refers to vofa_function.o(.data) for .data
    vofa_function.o(i.vofaJustFloatInit) refers to vofa_function.o(.bss) for .bss
    vofa_function.o(i.vofaSendFirewater) refers to f2d.o(.text) for __aeabi_f2d
    vofa_function.o(i.vofaSendFirewater) refers to printfa.o(i.__0snprintf) for __2snprintf
    vofa_function.o(i.vofaSendFirewater) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    vofa_function.o(i.vofaSendFirewater) refers to vofa_function.o(.bss) for .bss
    vofa_function.o(i.vofaSendFirewater) refers to usart.o(.bss) for huart2
    vofa_function.o(i.vofaSendInt32AsFloat) refers to fflti.o(.text) for __aeabi_i2f
    vofa_function.o(i.vofaSendInt32AsFloat) refers to vofa_function.o(i.vofaSendJustFloat) for vofaSendJustFloat
    vofa_function.o(i.vofaSendInt32AsFloat) refers to vofa_function.o(.bss) for .bss
    vofa_function.o(i.vofaSendIntAsFloat) refers to fflti.o(.text) for __aeabi_i2f
    vofa_function.o(i.vofaSendIntAsFloat) refers to vofa_function.o(i.vofaSendJustFloat) for vofaSendJustFloat
    vofa_function.o(i.vofaSendIntAsFloat) refers to vofa_function.o(.bss) for .bss
    vofa_function.o(i.vofaSendJustFloat) refers to vofa_function.o(i.float2uint8Array) for float2uint8Array
    vofa_function.o(i.vofaSendJustFloat) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    vofa_function.o(i.vofaSendJustFloat) refers to vofa_function.o(.bss) for .bss
    vofa_function.o(i.vofaSendJustFloat) refers to usart.o(.bss) for huart2
    vofa_function.o(i.vofaSendMixedDataAsFloat) refers to vofa_function.o(i.vofaSendJustFloat) for vofaSendJustFloat
    vofa_function.o(i.vofaSendMixedDataAsFloat) refers to vofa_function.o(.bss) for .bss
    vofa_function.o(i.vofaSendRawdata) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    vofa_function.o(i.vofaSendRawdata) refers to usart.o(.bss) for huart2
    vofa_function.o(i.vofaSendUint16AsFloat) refers to ffltui.o(.text) for __aeabi_ui2f
    vofa_function.o(i.vofaSendUint16AsFloat) refers to vofa_function.o(i.vofaSendJustFloat) for vofaSendJustFloat
    vofa_function.o(i.vofaSendUint16AsFloat) refers to vofa_function.o(.bss) for .bss
    stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f1xx_hal_adc.o(i.ADC_DMAError) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f1xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f1xx_hal_adc.o(i.ADC_Enable) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc.o(i.ADC_Enable) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f1xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f1xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f1xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f1xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f1xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) for HAL_RCCEx_GetPeriphCLKFreq
    stm32f1xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) for HAL_RCCEx_GetPeriphCLKFreq
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) for HAL_RCCEx_GetPeriphCLKFreq
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal_rcc.o(i.RCC_Delay) for RCC_Delay
    stm32f1xx_hal_rcc.o(i.RCC_Delay) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData) for FLASH_OB_ProgramData
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_can.o(i.HAL_CAN_DeInit) refers to stm32f1xx_hal_can.o(i.HAL_CAN_Stop) for HAL_CAN_Stop
    stm32f1xx_hal_can.o(i.HAL_CAN_DeInit) refers to can.o(i.HAL_CAN_MspDeInit) for HAL_CAN_MspDeInit
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback) for HAL_CAN_TxMailbox0CompleteCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback) for HAL_CAN_TxMailbox0AbortCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback) for HAL_CAN_TxMailbox1CompleteCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback) for HAL_CAN_TxMailbox1AbortCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback) for HAL_CAN_TxMailbox2CompleteCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback) for HAL_CAN_TxMailbox2AbortCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback) for HAL_CAN_RxFifo0FullCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to canopen.o(i.HAL_CAN_RxFifo0MsgPendingCallback) for HAL_CAN_RxFifo0MsgPendingCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback) for HAL_CAN_RxFifo1FullCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback) for HAL_CAN_RxFifo1MsgPendingCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_SleepCallback) for HAL_CAN_SleepCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback) for HAL_CAN_WakeUpFromRxMsgCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_ErrorCallback) for HAL_CAN_ErrorCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_Init) refers to can.o(i.HAL_CAN_MspInit) for HAL_CAN_MspInit
    stm32f1xx_hal_can.o(i.HAL_CAN_Init) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_can.o(i.HAL_CAN_Start) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_can.o(i.HAL_CAN_Stop) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f1xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f1xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32f1xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_spi.o(i.HAL_SPI_DeInit) refers to spi.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32f1xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f1xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32f1xx_hal_spi.o(i.HAL_SPI_Init) refers to spi.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f1xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f1xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAError) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f1xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f1xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f1xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAError) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to modbus.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) refers to modbus.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT) refers to modbus.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to modbus.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for .data
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for .constdata
    atof.o(i.__softfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__softfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    ldiv.o(.text) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to vofa_function.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to vofa_function.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to modbus.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to vofa_function.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to vofa_function.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to modbus.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to vofa_function.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to vofa_function.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to modbus.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to vofa_function.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to vofa_function.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to modbus.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to vofa_function.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to vofa_function.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to modbus.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to vofa_function.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to vofa_function.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to modbus.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to vofa_function.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to vofa_function.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to modbus.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to vofa_function.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to vofa_function.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to modbus.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to vofa_function.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to vofa_function.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to modbus.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to vofa_function.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to vofa_function.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to modbus.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to vofa_function.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to vofa_function.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to modbus.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to vofa_function.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to vofa_function.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to modbus.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to vofa_function.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to vofa_function.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to modbus.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to vofa_function.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to vofa_function.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to modbus.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to vofa_function.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to vofa_function.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to modbus.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to vofa_function.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to vofa_function.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to modbus.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to vofa_function.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to vofa_function.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to modbus.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to vofa_function.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to vofa_function.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to modbus.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to vofa_function.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to vofa_function.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to modbus.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to vofa_function.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to vofa_function.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to modbus.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to vofa_function.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to vofa_function.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to modbus.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to vofa_function.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to vofa_function.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to modbus.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dfltl.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltl.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixl.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixl.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixl.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    strtod.o(.text) refers to scanf_fp.o(.text) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace_c.o(.text) for isspace
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    scanf_fp.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    scanf_fp.o(.text) refers to dfltul.o(.text) for __aeabi_ul2d
    scanf_fp.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    scanf_fp.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    scanf_fp.o(.text) refers to scanf_fp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata
    dfltul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    scanf_fp.o(i._is_digit) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f103xb.o(HEAP), (512 bytes).
    Removing canopen.o(.rev16_text), (4 bytes).
    Removing canopen.o(.revsh_text), (4 bytes).
    Removing canopen.o(.rrx_text), (6 bytes).
    Removing canopen.o(.data), (8 bytes).
    Removing crc_16.o(.rev16_text), (4 bytes).
    Removing crc_16.o(.revsh_text), (4 bytes).
    Removing crc_16.o(.rrx_text), (6 bytes).
    Removing current_loop.o(.rev16_text), (4 bytes).
    Removing current_loop.o(.revsh_text), (4 bytes).
    Removing current_loop.o(.rrx_text), (6 bytes).
    Removing current_loop.o(.data), (4 bytes).
    Removing current_loop.o(.data), (4 bytes).
    Removing current_loop.o(.data), (4 bytes).
    Removing current_loop.o(.data), (2 bytes).
    Removing current_loop.o(.data), (2 bytes).
    Removing current_loop.o(.data), (2 bytes).
    Removing current_loop.o(.data), (4 bytes).
    Removing current_loop.o(.data), (4 bytes).
    Removing current_loop.o(.data), (4 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing delay.o(i.delay_us), (52 bytes).
    Removing dic.o(.rev16_text), (4 bytes).
    Removing dic.o(.revsh_text), (4 bytes).
    Removing dic.o(.rrx_text), (6 bytes).
    Removing dic.o(i.Cal_sum), (28 bytes).
    Removing dic.o(i.Check_sum), (24 bytes).
    Removing dic.o(i.Index_To_RSDO), (32 bytes).
    Removing dic.o(i.Index_To_WSDO), (108 bytes).
    Removing dic.o(i.RS232_Read_Index), (30 bytes).
    Removing dic.o(i.RS232_Write_Index), (32 bytes).
    Removing dic.o(i.RS485_SDO_Process), (176 bytes).
    Removing dic.o(i.RS485_SDO_Service), (2 bytes).
    Removing dic.o(i.Read_Parameter), (44 bytes).
    Removing dic.o(i.Read_eerom_OD), (52 bytes).
    Removing dic.o(i.Save_Parameter), (44 bytes).
    Removing dic.o(i.Search_eerom_OD), (44 bytes).
    Removing dic.o(i.VCP_Process), (268 bytes).
    Removing dic.o(i.VCP_Service_Process), (56 bytes).
    Removing dic.o(i.Write_eerom_OD), (52 bytes).
    Removing dic.o(.bss), (110 bytes).
    Removing dic.o(.bss), (28 bytes).
    Removing dic.o(.bss), (400 bytes).
    Removing ds402.o(.rev16_text), (4 bytes).
    Removing ds402.o(.revsh_text), (4 bytes).
    Removing ds402.o(.rrx_text), (6 bytes).
    Removing ds402.o(.data), (2 bytes).
    Removing modbus.o(.rev16_text), (4 bytes).
    Removing modbus.o(.revsh_text), (4 bytes).
    Removing modbus.o(.rrx_text), (6 bytes).
    Removing modbus.o(i.Modbus_Solve_PutString), (60 bytes).
    Removing modbus.o(i.RS232_03_Solve), (164 bytes).
    Removing modbus.o(i.RS232_06_Solve), (112 bytes).
    Removing modbus.o(i.RS232_16_Solve), (184 bytes).
    Removing modbus.o(i.RS232_Process), (2 bytes).
    Removing modbus.o(i.RS232_Solve_PutString), (24 bytes).
    Removing modbus.o(i.RS232_Solve_Service), (160 bytes).
    Removing modbus.o(i.RS485_Process), (48 bytes).
    Removing modbus.o(i._sys_exit), (2 bytes).
    Removing modbus.o(.data), (2 bytes).
    Removing modbus.o(.data), (4 bytes).
    Removing modbus.o(.data), (1 bytes).
    Removing motion_control.o(.rev16_text), (4 bytes).
    Removing motion_control.o(.revsh_text), (4 bytes).
    Removing motion_control.o(.rrx_text), (6 bytes).
    Removing motion_control.o(i.Auto_reserve_process), (260 bytes).
    Removing motion_control.o(.bss), (40 bytes).
    Removing motion_control.o(.bss), (40 bytes).
    Removing motion_control.o(.bss), (40 bytes).
    Removing motion_control.o(.bss), (40 bytes).
    Removing motion_control.o(.bss), (40 bytes).
    Removing motion_control.o(.data), (1024 bytes).
    Removing motion_control.o(.data), (4 bytes).
    Removing motion_control.o(.data), (4 bytes).
    Removing motion_control.o(.data), (4 bytes).
    Removing motion_control.o(.data), (4 bytes).
    Removing motion_control.o(.data), (4 bytes).
    Removing motion_control.o(.data), (2 bytes).
    Removing motion_control.o(.data), (2 bytes).
    Removing motion_control.o(.data), (2 bytes).
    Removing motion_control.o(.data), (2 bytes).
    Removing motion_control.o(.data), (2 bytes).
    Removing motion_control.o(.data), (2 bytes).
    Removing motion_control.o(.data), (4 bytes).
    Removing motion_control.o(.data), (4 bytes).
    Removing motion_control.o(.data), (4 bytes).
    Removing motion_control.o(.data), (4 bytes).
    Removing motion_control.o(.data), (4 bytes).
    Removing motion_control.o(.data), (4 bytes).
    Removing motion_control.o(.data), (4 bytes).
    Removing motion_control.o(.data), (4 bytes).
    Removing motion_control.o(.data), (4 bytes).
    Removing motion_control.o(.data), (4 bytes).
    Removing motion_control.o(.data), (8 bytes).
    Removing ntc_calculate.o(.rev16_text), (4 bytes).
    Removing ntc_calculate.o(.revsh_text), (4 bytes).
    Removing ntc_calculate.o(.rrx_text), (6 bytes).
    Removing parameter.o(.rev16_text), (4 bytes).
    Removing parameter.o(.revsh_text), (4 bytes).
    Removing parameter.o(.rrx_text), (6 bytes).
    Removing parameter.o(i.MemReadHalfWord), (48 bytes).
    Removing parameter.o(i.MemWriteHalfWord), (92 bytes).
    Removing parameter.o(i.MemWriteModbus), (108 bytes).
    Removing parameter.o(i.Process_Store_parameter), (104 bytes).
    Removing mcpwm.o(.rev16_text), (4 bytes).
    Removing mcpwm.o(.revsh_text), (4 bytes).
    Removing mcpwm.o(.rrx_text), (6 bytes).
    Removing mcpwm.o(i.safe_assert), (24 bytes).
    Removing mcpwm.o(.bss), (8192 bytes).
    Removing mcpwm.o(.bss), (16 bytes).
    Removing mcpwm.o(.bss), (20 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (4 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (4 bytes).
    Removing mcpwm.o(.data), (4 bytes).
    Removing mcpwm.o(.data), (4 bytes).
    Removing mcpwm.o(.data), (4 bytes).
    Removing mcpwm.o(.data), (4 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (4 bytes).
    Removing mcpwm.o(.data), (4 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (4 bytes).
    Removing mcpwm.o(.data), (4 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (2 bytes).
    Removing mcpwm.o(.data), (4 bytes).
    Removing mcpwm.o(.data), (8 bytes).
    Removing mcpwm.o(.data), (4 bytes).
    Removing position_loop.o(.rev16_text), (4 bytes).
    Removing position_loop.o(.revsh_text), (4 bytes).
    Removing position_loop.o(.rrx_text), (6 bytes).
    Removing position_loop.o(i.Error_process), (32 bytes).
    Removing sin_table.o(.rev16_text), (4 bytes).
    Removing sin_table.o(.revsh_text), (4 bytes).
    Removing sin_table.o(.rrx_text), (6 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.rrx_text), (6 bytes).
    Removing sys.o(i.MX_SYS_Init), (20 bytes).
    Removing velocity_loop.o(.rev16_text), (4 bytes).
    Removing velocity_loop.o(.revsh_text), (4 bytes).
    Removing velocity_loop.o(.rrx_text), (6 bytes).
    Removing velocity_loop.o(i.Low_pass_filter), (56 bytes).
    Removing velocity_loop.o(.bss), (128 bytes).
    Removing velocity_loop.o(.data), (4 bytes).
    Removing velocity_loop.o(.data), (4 bytes).
    Removing velocity_loop.o(.data), (4 bytes).
    Removing mt6825.o(.rev16_text), (4 bytes).
    Removing mt6825.o(.revsh_text), (4 bytes).
    Removing mt6825.o(.rrx_text), (6 bytes).
    Removing mt6825.o(i.Delay), (8 bytes).
    Removing mt6825.o(.data), (4 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (88 bytes).
    Removing can.o(.rev16_text), (4 bytes).
    Removing can.o(.revsh_text), (4 bytes).
    Removing can.o(.rrx_text), (6 bytes).
    Removing can.o(i.HAL_CAN_MspDeInit), (56 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing spi.o(i.HAL_SPI_MspDeInit), (40 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (56 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (152 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_it.o(.data), (4 bytes).
    Removing stm32f1xx_it.o(.data), (4 bytes).
    Removing stm32f1xx_it.o(.data), (4 bytes).
    Removing stm32f1xx_it.o(.data), (4 bytes).
    Removing stm32f1xx_it.o(.data), (4 bytes).
    Removing stm32f1xx_it.o(.data), (4 bytes).
    Removing stm32f1xx_it.o(.data), (4 bytes).
    Removing stm32f1xx_it.o(.data), (4 bytes).
    Removing stm32f1xx_it.o(.data), (4 bytes).
    Removing stm32f1xx_it.o(.data), (4 bytes).
    Removing stm32f1xx_it.o(.data), (4 bytes).
    Removing stm32f1xx_it.o(.data), (4 bytes).
    Removing stm32f1xx_it.o(.data), (4 bytes).
    Removing stm32f1xx_it.o(.data), (4 bytes).
    Removing stm32f1xx_it.o(.data), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing tamagawa.o(.rev16_text), (4 bytes).
    Removing tamagawa.o(.revsh_text), (4 bytes).
    Removing tamagawa.o(.rrx_text), (6 bytes).
    Removing tamagawa.o(.bss), (15 bytes).
    Removing tamagawa.o(.data), (2 bytes).
    Removing tamagawa.o(.data), (2 bytes).
    Removing tamagawa.o(.data), (2 bytes).
    Removing tamagawa.o(.data), (4 bytes).
    Removing tamagawa.o(.data), (4 bytes).
    Removing tamagawa.o(.data), (2 bytes).
    Removing tamagawa.o(.data), (2 bytes).
    Removing tamagawa.o(.data), (2 bytes).
    Removing tamagawa.o(.data), (2 bytes).
    Removing tamagawa.o(.data), (2 bytes).
    Removing tamagawa.o(.data), (2 bytes).
    Removing tamagawa.o(.data), (2 bytes).
    Removing tamagawa.o(.data), (2 bytes).
    Removing tamagawa.o(.data), (2 bytes).
    Removing tamagawa.o(.data), (2 bytes).
    Removing tamagawa.o(.data), (2 bytes).
    Removing tamagawa.o(.data), (4 bytes).
    Removing vofa_function.o(.rev16_text), (4 bytes).
    Removing vofa_function.o(.revsh_text), (4 bytes).
    Removing vofa_function.o(.rrx_text), (6 bytes).
    Removing vofa_function.o(i.float2uint8Array), (18 bytes).
    Removing vofa_function.o(i.uartSendByte), (20 bytes).
    Removing vofa_function.o(i.uartSendData), (16 bytes).
    Removing vofa_function.o(i.uint8Array2Float), (28 bytes).
    Removing vofa_function.o(i.vofaSendFirewater), (132 bytes).
    Removing vofa_function.o(i.vofaSendInt32AsFloat), (68 bytes).
    Removing vofa_function.o(i.vofaSendIntAsFloat), (52 bytes).
    Removing vofa_function.o(i.vofaSendJustFloat), (64 bytes).
    Removing vofa_function.o(i.vofaSendMixedDataAsFloat), (36 bytes).
    Removing vofa_function.o(i.vofaSendRawdata), (16 bytes).
    Removing vofa_function.o(i.vofaSendUint16AsFloat), (52 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (16 bytes).
    Removing stm32f1xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_adc.o(i.ADC_DMAConvCplt), (82 bytes).
    Removing stm32f1xx_hal_adc.o(i.ADC_DMAError), (26 bytes).
    Removing stm32f1xx_hal_adc.o(i.ADC_DMAHalfConvCplt), (10 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (124 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_DeInit), (228 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_PollForConversion), (376 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_PollForEvent), (106 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_Start), (228 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_Start_DMA), (304 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_Start_IT), (240 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_Stop), (52 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (94 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_Stop_IT), (66 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start), (268 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (360 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (196 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (84 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (98 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (136 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (32 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (272 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (144 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (32 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (268 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (60 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (172 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (44 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (252 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (300 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (42 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (14 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (104 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (580 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (80 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (88 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (86 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (28 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (42 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (42 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (94 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (28 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (148 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (26 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (26 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord), (28 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode), (100 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation), (104 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (284 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (4 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program), (148 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (104 bytes).
    Removing stm32f1xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (192 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (192 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData), (60 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (68 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (180 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (88 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (28 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (132 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (132 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (184 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (28 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (40 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (16 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (232 bytes).
    Removing stm32f1xx_hal_can.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_can.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_can.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_AbortTxRequest), (76 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_DeInit), (44 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_DeactivateNotification), (38 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_GetError), (4 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_GetRxFifoFillLevel), (38 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_GetState), (40 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_GetTxMailboxesFreeLevel), (50 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_GetTxTimestamp), (40 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_IsSleepActive), (30 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_IsTxMessagePending), (32 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_MspInit), (2 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_RequestSleep), (38 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_ResetError), (34 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo0MsgPendingCallback), (2 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_Stop), (108 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_WakeUp), (92 bytes).
    Removing stm32f1xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Abort), (304 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT), (316 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_DMAPause), (38 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_DMAResume), (38 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_DMAStop), (68 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_DeInit), (48 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_GetError), (4 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_GetState), (6 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_IRQHandler), (256 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_MspInit), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Receive), (366 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (256 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_IT), (188 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit), (412 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive), (504 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (324 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (176 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (220 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (172 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (48 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (48 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (48 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (48 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_AbortRx_ISR), (92 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_AbortTx_ISR), (28 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (164 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR), (76 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR), (136 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAAbortOnError), (16 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAError), (34 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (10 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (10 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (10 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt), (110 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback), (98 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt), (102 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (92 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMATxAbortCallback), (120 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction), (112 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction), (36 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_RxISR_16BIT), (32 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_RxISR_8BIT), (32 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_TxISR_16BIT), (32 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_TxISR_8BIT), (32 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout), (216 bytes).
    Removing stm32f1xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start), (88 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (176 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (230 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (440 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (448 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (28 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (126 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (28 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (126 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (206 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (180 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (532 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (220 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (176 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (240 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (216 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (42 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetChannelState), (36 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (180 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start), (232 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (492 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (290 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop), (114 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (200 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (172 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start), (176 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (444 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (236 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop), (114 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (196 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (172 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (246 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (96 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (102 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (122 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (106 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (126 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (444 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (196 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (172 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (44 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (82 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (82 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (62 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (62 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (24 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt), (24 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (148 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig), (102 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig), (64 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI3_SetConfig), (60 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI4_SetConfig), (64 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (118 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (148 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (118 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (36 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (212 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (138 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (204 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (148 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (64 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (72 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (74 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (170 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (388 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (224 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (106 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (166 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (104 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (128 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (106 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (126 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (388 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (224 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (166 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (94 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f1xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (54 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (54 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_SendBreak), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init), (146 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (282 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (86 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (86 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort), (166 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive), (108 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (128 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit), (80 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (100 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT), (204 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAPause), (102 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAResume), (98 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetState), (12 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive), (212 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_IT), (66 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback), (46 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback), (46 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (128 bytes).
    Removing ffltui.o(.text), (10 bytes).
    Removing f2d.o(.text), (38 bytes).
    Removing cdcmple.o(.text), (48 bytes).

689 unused section(s) (total 48276 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ../Core/Src/can.c                        0x00000000   Number         0  can.o ABSOLUTE
    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_adc.c 0x00000000   Number         0  stm32f1xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_adc_ex.c 0x00000000   Number         0  stm32f1xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_can.c 0x00000000   Number         0  stm32f1xx_hal_can.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_spi.c 0x00000000   Number         0  stm32f1xx_hal_spi.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_c.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  ldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  useno.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixl.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltl.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/atof.c                        0x00000000   Number         0  atof.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ..\Core\Src\Dic.c                        0x00000000   Number         0  dic.o ABSOLUTE
    ..\Core\Src\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\Core\Src\can.c                        0x00000000   Number         0  can.o ABSOLUTE
    ..\Core\Src\canopen.c                    0x00000000   Number         0  canopen.o ABSOLUTE
    ..\Core\Src\crc_16.c                     0x00000000   Number         0  crc_16.o ABSOLUTE
    ..\Core\Src\current_loop.c               0x00000000   Number         0  current_loop.o ABSOLUTE
    ..\Core\Src\delay.c                      0x00000000   Number         0  delay.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\ds402.c                      0x00000000   Number         0  ds402.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\mcpwm.c                      0x00000000   Number         0  mcpwm.o ABSOLUTE
    ..\Core\Src\modbus.c                     0x00000000   Number         0  modbus.o ABSOLUTE
    ..\Core\Src\motion_control.c             0x00000000   Number         0  motion_control.o ABSOLUTE
    ..\Core\Src\mt6825.c                     0x00000000   Number         0  mt6825.o ABSOLUTE
    ..\Core\Src\ntc_calculate.c              0x00000000   Number         0  ntc_calculate.o ABSOLUTE
    ..\Core\Src\parameter.c                  0x00000000   Number         0  parameter.o ABSOLUTE
    ..\Core\Src\position_loop.c              0x00000000   Number         0  position_loop.o ABSOLUTE
    ..\Core\Src\sin_table.c                  0x00000000   Number         0  sin_table.o ABSOLUTE
    ..\Core\Src\spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ..\Core\Src\sys.c                        0x00000000   Number         0  sys.o ABSOLUTE
    ..\Core\Src\system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ..\Core\Src\tamagawa.c                   0x00000000   Number         0  tamagawa.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Core\Src\utils.c                      0x00000000   Number         0  utils.o ABSOLUTE
    ..\Core\Src\velocity_loop.c              0x00000000   Number         0  velocity_loop.o ABSOLUTE
    ..\Core\Src\vofa_function.c              0x00000000   Number         0  vofa_function.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_adc.c 0x00000000   Number         0  stm32f1xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_adc_ex.c 0x00000000   Number         0  stm32f1xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_can.c 0x00000000   Number         0  stm32f1xx_hal_can.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_spi.c 0x00000000   Number         0  stm32f1xx_hal_spi.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ..\\Core\\Src\\Dic.c                     0x00000000   Number         0  dic.o ABSOLUTE
    ..\\Core\\Src\\canopen.c                 0x00000000   Number         0  canopen.o ABSOLUTE
    ..\\Core\\Src\\crc_16.c                  0x00000000   Number         0  crc_16.o ABSOLUTE
    ..\\Core\\Src\\current_loop.c            0x00000000   Number         0  current_loop.o ABSOLUTE
    ..\\Core\\Src\\delay.c                   0x00000000   Number         0  delay.o ABSOLUTE
    ..\\Core\\Src\\ds402.c                   0x00000000   Number         0  ds402.o ABSOLUTE
    ..\\Core\\Src\\mcpwm.c                   0x00000000   Number         0  mcpwm.o ABSOLUTE
    ..\\Core\\Src\\modbus.c                  0x00000000   Number         0  modbus.o ABSOLUTE
    ..\\Core\\Src\\motion_control.c          0x00000000   Number         0  motion_control.o ABSOLUTE
    ..\\Core\\Src\\mt6825.c                  0x00000000   Number         0  mt6825.o ABSOLUTE
    ..\\Core\\Src\\ntc_calculate.c           0x00000000   Number         0  ntc_calculate.o ABSOLUTE
    ..\\Core\\Src\\parameter.c               0x00000000   Number         0  parameter.o ABSOLUTE
    ..\\Core\\Src\\position_loop.c           0x00000000   Number         0  position_loop.o ABSOLUTE
    ..\\Core\\Src\\sin_table.c               0x00000000   Number         0  sin_table.o ABSOLUTE
    ..\\Core\\Src\\sys.c                     0x00000000   Number         0  sys.o ABSOLUTE
    ..\\Core\\Src\\tamagawa.c                0x00000000   Number         0  tamagawa.o ABSOLUTE
    ..\\Core\\Src\\velocity_loop.c           0x00000000   Number         0  velocity_loop.o ABSOLUTE
    ..\\Core\\Src\\vofa_function.c           0x00000000   Number         0  vofa_function.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f103xb.s                    0x00000000   Number         0  startup_stm32f103xb.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f103xb.o(RESET)
    .ARM.Collect$$$$00000000                 0x080000ec   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080000ec   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080000f0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080000f4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080000f4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080000f4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x080000fc   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x08000100   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x08000100   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x08000100   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000100   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000104   Section       36  startup_stm32f103xb.o(.text)
    .text                                    0x08000128   Section        0  ldiv.o(.text)
    .text                                    0x0800018a   Section        0  llushr.o(.text)
    .text                                    0x080001aa   Section        0  memcpya.o(.text)
    .text                                    0x080001ce   Section        0  memseta.o(.text)
    .text                                    0x080001f2   Section        0  fadd.o(.text)
    .text                                    0x080002a2   Section        0  fmul.o(.text)
    .text                                    0x08000306   Section        0  fdiv.o(.text)
    .text                                    0x08000382   Section        0  fflti.o(.text)
    .text                                    0x08000394   Section        0  dfltl.o(.text)
    .text                                    0x080003bc   Section        0  ffixi.o(.text)
    .text                                    0x080003ee   Section        0  dfixl.o(.text)
    .text                                    0x08000438   Section        0  d2f.o(.text)
    .text                                    0x08000470   Section        0  uidiv.o(.text)
    .text                                    0x0800049c   Section        0  uldiv.o(.text)
    .text                                    0x080004fe   Section        0  llshl.o(.text)
    .text                                    0x0800051c   Section        0  strtod.o(.text)
    _local_sscanf                            0x0800051d   Thumb Code    54  strtod.o(.text)
    .text                                    0x080005b8   Section        0  iusefp.o(.text)
    .text                                    0x080005b8   Section        0  fepilogue.o(.text)
    .text                                    0x08000626   Section        0  depilogue.o(.text)
    .text                                    0x080006e0   Section        0  dadd.o(.text)
    .text                                    0x0800082e   Section        0  dmul.o(.text)
    .text                                    0x08000912   Section        0  ddiv.o(.text)
    .text                                    0x080009f0   Section        0  dsqrt.o(.text)
    .text                                    0x08000a92   Section        0  dfixul.o(.text)
    .text                                    0x08000ac4   Section       48  cdrcmple.o(.text)
    .text                                    0x08000af4   Section       36  init.o(.text)
    .text                                    0x08000b18   Section        0  llsshr.o(.text)
    .text                                    0x08000b3c   Section        0  isspace_c.o(.text)
    .text                                    0x08000b48   Section        0  scanf_fp.o(.text)
    _fp_value                                0x08000b49   Thumb Code   296  scanf_fp.o(.text)
    .text                                    0x08000ea8   Section        0  _sgetc.o(.text)
    .text                                    0x08000ee8   Section        0  ctype_c.o(.text)
    .text                                    0x08000f10   Section        0  dfltul.o(.text)
    .text                                    0x08000f28   Section        0  __dczerorl2.o(.text)
    i.ADC1_2_IRQHandler                      0x08000f80   Section        0  stm32f1xx_it.o(i.ADC1_2_IRQHandler)
    i.ADC_ConversionStop_Disable             0x08000f9c   Section        0  stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable)
    i.ADC_Enable                             0x08000ff8   Section        0  stm32f1xx_hal_adc.o(i.ADC_Enable)
    i.Acce_distance_cal                      0x08001080   Section        0  motion_control.o(i.Acce_distance_cal)
    i.BusFault_Handler                       0x080010c4   Section        0  stm32f1xx_it.o(i.BusFault_Handler)
    i.CAN_User_Init                          0x080010c8   Section        0  canopen.o(i.CAN_User_Init)
    i.Calibrate_ADC_Offset                   0x08001148   Section        0  mcpwm.o(i.Calibrate_ADC_Offset)
    i.Check_DCBus                            0x080011f0   Section        0  position_loop.o(i.Check_DCBus)
    i.Check_IIt                              0x08001274   Section        0  position_loop.o(i.Check_IIt)
    i.Check_Temperature                      0x080012e0   Section        0  position_loop.o(i.Check_Temperature)
    i.Current_loop                           0x08001308   Section        0  current_loop.o(i.Current_loop)
    i.DMA1_Channel2_IRQHandler               0x08001564   Section        0  stm32f1xx_it.o(i.DMA1_Channel2_IRQHandler)
    i.DMA1_Channel3_IRQHandler               0x08001570   Section        0  stm32f1xx_it.o(i.DMA1_Channel3_IRQHandler)
    i.DMA1_Channel5_IRQHandler               0x0800157c   Section        0  stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler)
    i.DMA1_Channel6_IRQHandler               0x08001588   Section        0  stm32f1xx_it.o(i.DMA1_Channel6_IRQHandler)
    i.DMA1_Channel7_IRQHandler               0x08001594   Section        0  stm32f1xx_it.o(i.DMA1_Channel7_IRQHandler)
    i.DMA_SetConfig                          0x080015a0   Section        0  stm32f1xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x080015a1   Thumb Code    56  stm32f1xx_hal_dma.o(i.DMA_SetConfig)
    i.DS402_process                          0x080015d8   Section        0  ds402.o(i.DS402_process)
    i.DebugMon_Handler                       0x080016e4   Section        0  stm32f1xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x080016e6   Section        0  main.o(i.Error_Handler)
    i.Exchange_motor_code                    0x080016e8   Section        0  parameter.o(i.Exchange_motor_code)
    i.Get_Crc16                              0x080017dc   Section        0  crc_16.o(i.Get_Crc16)
    i.Get_NTC_Temperature                    0x08001808   Section        0  ntc_calculate.o(i.Get_NTC_Temperature)
    i.HAL_ADCEx_InjectedConfigChannel        0x0800185c   Section        0  stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel)
    i.HAL_ADCEx_InjectedConvCpltCallback     0x08001aac   Section        0  stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback)
    i.HAL_ADCEx_InjectedGetValue             0x08001aae   Section        0  stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue)
    i.HAL_ADCEx_InjectedStart                0x08001acc   Section        0  stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart)
    i.HAL_ADC_ConfigChannel                  0x08001b84   Section        0  stm32f1xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x08001cac   Section        0  stm32f1xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_GetValue                       0x08001cae   Section        0  stm32f1xx_hal_adc.o(i.HAL_ADC_GetValue)
    i.HAL_ADC_IRQHandler                     0x08001cb4   Section        0  stm32f1xx_hal_adc.o(i.HAL_ADC_IRQHandler)
    i.HAL_ADC_Init                           0x08001db4   Section        0  stm32f1xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_LevelOutOfWindowCallback       0x08001ed4   Section        0  stm32f1xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback)
    i.HAL_ADC_MspInit                        0x08001ed8   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_CAN_ActivateNotification           0x08001fa0   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_ActivateNotification)
    i.HAL_CAN_AddTxMessage                   0x08001fc4   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_AddTxMessage)
    i.HAL_CAN_ConfigFilter                   0x080020c8   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_ConfigFilter)
    i.HAL_CAN_ErrorCallback                  0x080021d0   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_ErrorCallback)
    i.HAL_CAN_GetRxMessage                   0x080021d2   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_GetRxMessage)
    i.HAL_CAN_IRQHandler                     0x0800233e   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler)
    i.HAL_CAN_Init                           0x08002572   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_Init)
    i.HAL_CAN_MspInit                        0x080026dc   Section        0  can.o(i.HAL_CAN_MspInit)
    i.HAL_CAN_RxFifo0FullCallback            0x08002758   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback)
    i.HAL_CAN_RxFifo0MsgPendingCallback      0x0800275c   Section        0  canopen.o(i.HAL_CAN_RxFifo0MsgPendingCallback)
    i.HAL_CAN_RxFifo1FullCallback            0x080027a0   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback)
    i.HAL_CAN_RxFifo1MsgPendingCallback      0x080027a2   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback)
    i.HAL_CAN_SleepCallback                  0x080027a4   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_SleepCallback)
    i.HAL_CAN_Start                          0x080027a6   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_Start)
    i.HAL_CAN_TxMailbox0AbortCallback        0x0800280a   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback)
    i.HAL_CAN_TxMailbox0CompleteCallback     0x0800280c   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback)
    i.HAL_CAN_TxMailbox1AbortCallback        0x0800280e   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback)
    i.HAL_CAN_TxMailbox1CompleteCallback     0x08002810   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback)
    i.HAL_CAN_TxMailbox2AbortCallback        0x08002812   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback)
    i.HAL_CAN_TxMailbox2CompleteCallback     0x08002814   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback)
    i.HAL_CAN_WakeUpFromRxMsgCallback        0x08002816   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback)
    i.HAL_DMA_Abort                          0x08002818   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08002860   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08002904   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08002a74   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08002af8   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08002b74   Section        0  stm32f1xx_hal.o(i.HAL_Delay)
    i.HAL_FLASH_Lock                         0x08002b9c   Section        0  stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock)
    i.HAL_FLASH_Unlock                       0x08002bb0   Section        0  stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock)
    i.HAL_GPIO_Init                          0x08002bdc   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_TogglePin                     0x08002dd8   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    i.HAL_GPIO_WritePin                      0x08002de8   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08002df8   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08002e04   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08002e14   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08002e38   Section        0  stm32f1xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08002e7c   Section        0  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08002ee0   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08002efc   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08002f64   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCCEx_PeriphCLKConfig              0x08002f88   Section        0  stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x080030b8   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08003238   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08003244   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08003264   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08003284   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x080032fc   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SPI_Init                           0x08003728   Section        0  stm32f1xx_hal_spi.o(i.HAL_SPI_Init)
    i.HAL_SPI_MspInit                        0x080037dc   Section        0  spi.o(i.HAL_SPI_MspInit)
    i.HAL_SYSTICK_CLKSourceConfig            0x0800384c   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig)
    i.HAL_SYSTICK_Config                     0x08003868   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_BreakCallback                0x08003890   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x08003892   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x08003894   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_MasterConfigSynchronization  0x080038fa   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIMEx_PWMN_Start                   0x0800396c   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start)
    i.HAL_TIMEx_PWMN_Stop                    0x08003a16   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop)
    i.HAL_TIM_Base_Init                      0x08003a80   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08003adc   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08003b40   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_ConfigClockSource              0x08003ba2   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_IC_CaptureCallback             0x08003c86   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x08003c88   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x08003dfc   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_ConfigChannel               0x08003e70   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08003ebc   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_OC_Init                        0x08003ebe   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init)
    i.HAL_TIM_OC_MspInit                     0x08003f1a   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit)
    i.HAL_TIM_PWM_ConfigChannel              0x08003f1c   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08003fee   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x0800404a   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x0800404c   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x08004050   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PWM_Start_IT                   0x08004100   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT)
    i.HAL_TIM_PWM_Stop                       0x080041ec   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop)
    i.HAL_TIM_PeriodElapsedCallback          0x0800425e   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08004260   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_RxEventCallback             0x08004262   Section        0  stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_DMAStop                       0x08004264   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x080042bc   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x080042c0   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x080044c0   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08004524   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_DMA                   0x08004750   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA)
    i.HAL_UART_Receive_IT                    0x0800477e   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x080047ac   Section        0  modbus.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x08004954   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x08004956   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_Transmit_DMA                  0x08004a20   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA)
    i.HAL_UART_TxCpltCallback                0x08004ab8   Section        0  modbus.o(i.HAL_UART_TxCpltCallback)
    i.HAL_UART_TxHalfCpltCallback            0x08004af8   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback)
    i.HardFault_Handler                      0x08004afa   Section        0  stm32f1xx_it.o(i.HardFault_Handler)
    i.IIt_DC_filter                          0x08004afc   Section        0  current_loop.o(i.IIt_DC_filter)
    i.IIt_filter                             0x08004b2c   Section        0  current_loop.o(i.IIt_filter)
    i.Init_Control_Parameter                 0x08004b5c   Section        0  parameter.o(i.Init_Control_Parameter)
    i.Init_Driver_State                      0x08004cc4   Section        0  parameter.o(i.Init_Driver_State)
    i.Init_Modbus_Addr_List                  0x08004cec   Section        0  modbus.o(i.Init_Modbus_Addr_List)
    i.Init_Motor_Parameter                   0x080050c4   Section        0  parameter.o(i.Init_Motor_Parameter)
    i.Init_System_Parameter                  0x08005134   Section        0  parameter.o(i.Init_System_Parameter)
    i.Init_current_loop_state                0x080051c4   Section        0  current_loop.o(i.Init_current_loop_state)
    i.Init_position_loop_state               0x080051e0   Section        0  position_loop.o(i.Init_position_loop_state)
    i.Init_velocity_loop_state               0x080051ec   Section        0  velocity_loop.o(i.Init_velocity_loop_state)
    i.LED_Process                            0x08005200   Section        0  motion_control.o(i.LED_Process)
    i.Low_pass_filter_1                      0x080052cc   Section        0  velocity_loop.o(i.Low_pass_filter_1)
    i.MX_ADC1_Init                           0x080052dc   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_ADC2_Init                           0x0800535c   Section        0  adc.o(i.MX_ADC2_Init)
    i.MX_CAN_Init                            0x080053dc   Section        0  can.o(i.MX_CAN_Init)
    i.MX_DMA_Init                            0x08005420   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x0800548c   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_SPI1_Init                           0x08005580   Section        0  spi.o(i.MX_SPI1_Init)
    i.MX_TIM1_Init                           0x080055cc   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM4_Init                           0x080056dc   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_USART1_UART_Init                    0x08005744   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08005780   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART3_UART_Init                    0x080057b8   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.MemManage_Handler                      0x080057f0   Section        0  stm32f1xx_it.o(i.MemManage_Handler)
    i.MemReadModbus                          0x080057f4   Section        0  parameter.o(i.MemReadModbus)
    i.Modbus_03_Solve                        0x0800582c   Section        0  modbus.o(i.Modbus_03_Solve)
    i.Modbus_06_Solve                        0x080058d0   Section        0  modbus.o(i.Modbus_06_Solve)
    i.Modbus_16_Solve                        0x08005940   Section        0  modbus.o(i.Modbus_16_Solve)
    i.Modbus_Solve_485_Disenable             0x080059f8   Section        0  modbus.o(i.Modbus_Solve_485_Disenable)
    i.Modbus_Solve_485_Enable                0x08005a08   Section        0  modbus.o(i.Modbus_Solve_485_Enable)
    i.Modbus_Solve_Service                   0x08005a18   Section        0  modbus.o(i.Modbus_Solve_Service)
    i.Motion_process                         0x08005abc   Section        0  motion_control.o(i.Motion_process)
    i.NMI_Handler                            0x08005e24   Section        0  stm32f1xx_it.o(i.NMI_Handler)
    i.OC4_PWM_Override                       0x08005e26   Section        0  tim.o(i.OC4_PWM_Override)
    i.PendSV_Handler                         0x08005e4a   Section        0  stm32f1xx_it.o(i.PendSV_Handler)
    i.Position_Loop                          0x08005e4c   Section        0  position_loop.o(i.Position_Loop)
    i.Process_RPDO                           0x08005f74   Section        0  canopen.o(i.Process_RPDO)
    i.Process_TPDO                           0x080060e8   Section        0  canopen.o(i.Process_TPDO)
    i.RCC_Delay                              0x0800616c   Section        0  stm32f1xx_hal_rcc.o(i.RCC_Delay)
    RCC_Delay                                0x0800616d   Thumb Code    32  stm32f1xx_hal_rcc.o(i.RCC_Delay)
    i.RCC_IRQHandler                         0x08006190   Section        0  stm32f1xx_it.o(i.RCC_IRQHandler)
    i.ReadValue                              0x08006194   Section        0  mt6825.o(i.ReadValue)
    i.Read_OD                                0x080061ec   Section        0  dic.o(i.Read_OD)
    i.SDO_Process                            0x08006224   Section        0  canopen.o(i.SDO_Process)
    i.SPIx_ReadWriteByte                     0x08006384   Section        0  mt6825.o(i.SPIx_ReadWriteByte)
    i.SVC_Handler                            0x080063bc   Section        0  stm32f1xx_it.o(i.SVC_Handler)
    i.SVM                                    0x080063be   Section        0  utils.o(i.SVM)
    i.Search_OD                              0x08006508   Section        0  dic.o(i.Search_OD)
    i.SysTick_Handler                        0x0800653c   Section        0  stm32f1xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x0800654a   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x080065aa   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.TIM1_CC_IRQHandler                     0x080065ac   Section        0  stm32f1xx_it.o(i.TIM1_CC_IRQHandler)
    i.TIM4_IRQHandler                        0x08006798   Section        0  stm32f1xx_it.o(i.TIM4_IRQHandler)
    i.TIM_Base_SetConfig                     0x080067e8   Section        0  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x08006860   Section        0  stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_CCxNChannelCmd                     0x0800687e   Section        0  stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd)
    TIM_CCxNChannelCmd                       0x0800687f   Thumb Code    30  stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd)
    i.TIM_ETR_SetConfig                      0x0800689c   Section        0  stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x080068b2   Section        0  stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x080068b3   Thumb Code    16  stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x080068c4   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x080068c5   Thumb Code    98  stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x0800692c   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08006994   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08006995   Thumb Code   100  stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x080069fc   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x080069fd   Thumb Code    78  stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08006a4a   Section        0  stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08006a4b   Thumb Code    38  stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x08006a70   Section        0  stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08006a71   Thumb Code    36  stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.Tamagawa_Read_Cmd                      0x08006a94   Section        0  tamagawa.o(i.Tamagawa_Read_Cmd)
    i.UART_DMAAbortOnError                   0x08006b24   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08006b25   Thumb Code    16  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x08006b34   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x08006b35   Thumb Code    74  stm32f1xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x08006b7e   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x08006b7f   Thumb Code    92  stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08006bda   Section        0  stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08006bdb   Thumb Code    26  stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_DMATransmitCplt                   0x08006bf4   Section        0  stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt)
    UART_DMATransmitCplt                     0x08006bf5   Thumb Code    48  stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt)
    i.UART_DMATxHalfCplt                     0x08006c24   Section        0  stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt)
    UART_DMATxHalfCplt                       0x08006c25   Thumb Code    10  stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt)
    i.UART_EndRxTransfer                     0x08006c2e   Section        0  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08006c2f   Thumb Code    48  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x08006c5e   Section        0  stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x08006c5f   Thumb Code    26  stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_EndTxTransfer                     0x08006c78   Section        0  stm32f1xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08006c79   Thumb Code    18  stm32f1xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x08006c8a   Section        0  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08006c8b   Thumb Code   190  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08006d48   Section        0  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08006d49   Thumb Code   194  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x08006e10   Section        0  stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_Start_Receive_IT                  0x08006e84   Section        0  stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_Transmit_IT                       0x08006eba   Section        0  stm32f1xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x08006ebb   Thumb Code    94  stm32f1xx_hal_uart.o(i.UART_Transmit_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08006f18   Section        0  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08006f19   Thumb Code   120  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08006f90   Section        0  stm32f1xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08006f9c   Section        0  stm32f1xx_it.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08007040   Section        0  stm32f1xx_it.o(i.USART3_IRQHandler)
    i.USB_LP_CAN1_RX0_IRQHandler             0x080070c4   Section        0  stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler)
    i.Update_Speed                           0x080070d0   Section        0  velocity_loop.o(i.Update_Speed)
    i.UsageFault_Handler                     0x08007170   Section        0  stm32f1xx_it.o(i.UsageFault_Handler)
    i.Velocity_loop                          0x08007174   Section        0  velocity_loop.o(i.Velocity_loop)
    i.Write_OD                               0x080072c4   Section        0  dic.o(i.Write_OD)
    i.__0printf                              0x080072fc   Section        0  printfa.o(i.__0printf)
    i.__read_errno                           0x0800731c   Section        0  errno.o(i.__read_errno)
    i.__scatterload_copy                     0x08007328   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08007336   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08007338   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x08007348   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x08007354   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08007355   Thumb Code   366  printfa.o(i._fp_digits)
    i._is_digit                              0x080074d8   Section        0  scanf_fp.o(i._is_digit)
    i._printf_core                           0x080074e8   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x080074e9   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08007b9c   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08007b9d   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08007bc0   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08007bc1   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i.arm_cos_f32                            0x08007bf0   Section        0  sin_table.o(i.arm_cos_f32)
    i.arm_sin_f32                            0x08007c2c   Section        0  sin_table.o(i.arm_sin_f32)
    i.atof                                   0x08007c64   Section        0  atof.o(i.atof)
    i.delay_init                             0x08007c90   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x08007ca4   Section        0  delay.o(i.delay_ms)
    i.fdummy                                 0x08007ca8   Section        0  dic.o(i.fdummy)
    i.find_commutation                       0x08007cac   Section        0  mcpwm.o(i.find_commutation)
    i.fputc                                  0x08007e70   Section        0  vofa_function.o(i.fputc)
    i.get_electric_phase                     0x08007e88   Section        0  mcpwm.o(i.get_electric_phase)
    i.init_motor_control                     0x08007eac   Section        0  mcpwm.o(i.init_motor_control)
    i.main                                   0x08007f24   Section        0  main.o(i.main)
    i.phase_current_from_adcval              0x08008050   Section        0  mcpwm.o(i.phase_current_from_adcval)
    i.queue_modulation_timings               0x08008070   Section        0  mcpwm.o(i.queue_modulation_timings)
    i.sqrt                                   0x080080bc   Section        0  sqrt.o(i.sqrt)
    i.start_adc                              0x08008108   Section        0  mcpwm.o(i.start_adc)
    i.start_pwm                              0x08008178   Section        0  mcpwm.o(i.start_pwm)
    i.stop_pwm                               0x080081c0   Section        0  mcpwm.o(i.stop_pwm)
    i.uart1_init                             0x08008208   Section        0  tamagawa.o(i.uart1_init)
    i.uart2_init                             0x08008240   Section        0  modbus.o(i.uart2_init)
    i.uart3_init                             0x08008294   Section        0  modbus.o(i.uart3_init)
    i.uartCMDRecv                            0x080082d8   Section        0  vofa_function.o(i.uartCMDRecv)
    i.update_motor                           0x08008318   Section        0  mcpwm.o(i.update_motor)
    i.vofaCommandParse                       0x080083fc   Section        0  vofa_function.o(i.vofaCommandParse)
    i.vofaJustFloatInit                      0x0800858c   Section        0  vofa_function.o(i.vofaJustFloatInit)
    .constdata                               0x080085ac   Section      512  crc_16.o(.constdata)
    .constdata                               0x080087ac   Section     1036  dic.o(.constdata)
    .constdata                               0x08008bb8   Section     4096  sin_table.o(.constdata)
    .constdata                               0x08009bb8   Section       16  system_stm32f1xx.o(.constdata)
    .constdata                               0x08009bc8   Section        8  system_stm32f1xx.o(.constdata)
    .constdata                               0x08009bd0   Section       64  ctype_c.o(.constdata)
    .data                                    0x20000000   Section       26  canopen.o(.data)
    .data                                    0x2000001a   Section        1  canopen.o(.data)
    .data                                    0x2000001c   Section        2  canopen.o(.data)
    .data                                    0x2000001e   Section        2  canopen.o(.data)
    .data                                    0x20000020   Section      108  current_loop.o(.data)
    .data                                    0x2000008c   Section        2  current_loop.o(.data)
    .data                                    0x20000090   Section        4  current_loop.o(.data)
    .data                                    0x20000094   Section        4  current_loop.o(.data)
    .data                                    0x20000098   Section        4  current_loop.o(.data)
    .data                                    0x2000009c   Section        4  current_loop.o(.data)
    .data                                    0x200000a0   Section        2  current_loop.o(.data)
    .data                                    0x200000a2   Section        2  current_loop.o(.data)
    .data                                    0x200000a4   Section        4  delay.o(.data)
    fac_us                                   0x200000a4   Data           4  delay.o(.data)
    .data                                    0x200000a8   Section        4  dic.o(.data)
    .data                                    0x200000ac   Section      104  dic.o(.data)
    .data                                    0x20000114   Section        8  ds402.o(.data)
    .data                                    0x2000011c   Section        2  ds402.o(.data)
    .data                                    0x20000120   Section       40  modbus.o(.data)
    .data                                    0x20000148   Section        4  modbus.o(.data)
    .data                                    0x2000014c   Section        4  modbus.o(.data)
    .data                                    0x20000150   Section       96  motion_control.o(.data)
    .data                                    0x200001b0   Section        4  motion_control.o(.data)
    .data                                    0x200001b4   Section        4  motion_control.o(.data)
    .data                                    0x200001b8   Section        4  motion_control.o(.data)
    .data                                    0x200001bc   Section        4  motion_control.o(.data)
    .data                                    0x200001c0   Section        1  motion_control.o(.data)
    .data                                    0x200001c4   Section        4  motion_control.o(.data)
    .data                                    0x200001c8   Section        4  motion_control.o(.data)
    .data                                    0x200001cc   Section        2  motion_control.o(.data)
    .data                                    0x200001d0   Section        4  motion_control.o(.data)
    .data                                    0x200001d4   Section       84  ntc_calculate.o(.data)
    .data                                    0x20000228   Section        4  ntc_calculate.o(.data)
    .data                                    0x2000022c   Section        4  parameter.o(.data)
    .data                                    0x20000230   Section        2  mcpwm.o(.data)
    .data                                    0x20000232   Section        2  mcpwm.o(.data)
    .data                                    0x20000234   Section      140  mcpwm.o(.data)
    .data                                    0x200002c0   Section        2  mcpwm.o(.data)
    .data                                    0x200002c4   Section        4  mcpwm.o(.data)
    .data                                    0x200002c8   Section        4  mcpwm.o(.data)
    .data                                    0x200002cc   Section        4  mcpwm.o(.data)
    .data                                    0x200002d0   Section        4  mcpwm.o(.data)
    .data                                    0x200002d4   Section        4  mcpwm.o(.data)
    .data                                    0x200002d8   Section        4  mcpwm.o(.data)
    .data                                    0x200002dc   Section        4  mcpwm.o(.data)
    .data                                    0x200002e0   Section        4  mcpwm.o(.data)
    .data                                    0x200002e4   Section        4  mcpwm.o(.data)
    .data                                    0x200002e8   Section        4  mcpwm.o(.data)
    .data                                    0x200002ec   Section        4  mcpwm.o(.data)
    .data                                    0x200002f0   Section        4  mcpwm.o(.data)
    .data                                    0x200002f4   Section        4  mcpwm.o(.data)
    .data                                    0x200002f8   Section        4  mcpwm.o(.data)
    .data                                    0x200002fc   Section        4  mcpwm.o(.data)
    .data                                    0x20000300   Section        2  mcpwm.o(.data)
    .data                                    0x20000304   Section        4  mcpwm.o(.data)
    .data                                    0x20000308   Section        2  mcpwm.o(.data)
    .data                                    0x2000030a   Section        2  mcpwm.o(.data)
    .data                                    0x2000030c   Section        2  mcpwm.o(.data)
    .data                                    0x20000310   Section        4  mcpwm.o(.data)
    .data                                    0x20000314   Section        4  mcpwm.o(.data)
    .data                                    0x20000318   Section        2  mcpwm.o(.data)
    .data                                    0x2000031c   Section        4  mcpwm.o(.data)
    .data                                    0x20000320   Section        4  mcpwm.o(.data)
    .data                                    0x20000324   Section        4  mcpwm.o(.data)
    .data                                    0x20000328   Section        4  mcpwm.o(.data)
    .data                                    0x2000032c   Section        4  mcpwm.o(.data)
    .data                                    0x20000330   Section        2  mcpwm.o(.data)
    .data                                    0x20000332   Section        2  mcpwm.o(.data)
    .data                                    0x20000334   Section        2  mcpwm.o(.data)
    .data                                    0x20000336   Section        2  mcpwm.o(.data)
    .data                                    0x20000338   Section       48  position_loop.o(.data)
    .data                                    0x20000368   Section        2  position_loop.o(.data)
    .data                                    0x2000036a   Section        2  sin_table.o(.data)
    .data                                    0x2000036c   Section       80  velocity_loop.o(.data)
    .data                                    0x200003bc   Section        2  velocity_loop.o(.data)
    .data                                    0x200003be   Section       12  mt6825.o(.data)
    .data                                    0x200003cc   Section        4  stm32f1xx_it.o(.data)
    .data                                    0x200003d0   Section        6  tamagawa.o(.data)
    .data                                    0x200003d8   Section        4  tamagawa.o(.data)
    .data                                    0x200003dc   Section        2  tamagawa.o(.data)
    .data                                    0x200003de   Section        2  tamagawa.o(.data)
    .data                                    0x200003e0   Section        2  tamagawa.o(.data)
    .data                                    0x200003e2   Section        2  tamagawa.o(.data)
    .data                                    0x200003e4   Section        2  tamagawa.o(.data)
    .data                                    0x200003e6   Section        2  tamagawa.o(.data)
    .data                                    0x200003e8   Section        2  tamagawa.o(.data)
    .data                                    0x200003ea   Section        2  tamagawa.o(.data)
    .data                                    0x200003ec   Section        4  tamagawa.o(.data)
    .data                                    0x200003f0   Section        4  tamagawa.o(.data)
    .data                                    0x200003f4   Section        4  tamagawa.o(.data)
    .data                                    0x200003f8   Section        2  tamagawa.o(.data)
    .data                                    0x200003fa   Section        2  tamagawa.o(.data)
    .data                                    0x200003fc   Section        2  tamagawa.o(.data)
    .data                                    0x200003fe   Section        1  vofa_function.o(.data)
    .data                                    0x20000400   Section       12  stm32f1xx_hal.o(.data)
    .data                                    0x2000040c   Section        4  system_stm32f1xx.o(.data)
    .data                                    0x20000410   Section        4  errno.o(.data)
    _errno                                   0x20000410   Data           4  errno.o(.data)
    .bss                                     0x20000414   Section      104  canopen.o(.bss)
    .bss                                     0x2000047c   Section     2800  modbus.o(.bss)
    .bss                                     0x20000f6c   Section       48  mcpwm.o(.bss)
    .bss                                     0x20000f9c   Section       96  adc.o(.bss)
    .bss                                     0x20000ffc   Section       40  can.o(.bss)
    .bss                                     0x20001024   Section       88  spi.o(.bss)
    .bss                                     0x2000107c   Section      144  tim.o(.bss)
    .bss                                     0x2000110c   Section      544  usart.o(.bss)
    .bss                                     0x2000132c   Section      200  tamagawa.o(.bss)
    .bss                                     0x200013f4   Section      200  tamagawa.o(.bss)
    .bss                                     0x200014bc   Section      316  vofa_function.o(.bss)
    buffer                                   0x200014bc   Data         256  vofa_function.o(.bss)
    STACK                                    0x200015f8   Section     1024  startup_stm32f103xb.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __use_no_errno                           0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_exception_handling              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_fp                              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap                            0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap_region                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting_swi                 0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_signal_handling                 0x00000000   Number         0  useno.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f103xb.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xb.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f103xb.o(RESET)
    __main                                   0x080000ed   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080000ed   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080000f1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080000f5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080000f5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080000f5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080000f5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x080000fd   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x08000101   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x08000101   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x08000105   Thumb Code     8  startup_stm32f103xb.o(.text)
    CAN1_RX1_IRQHandler                      0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_SCE_IRQHandler                      0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI0_IRQHandler                         0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI15_10_IRQHandler                     0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI1_IRQHandler                         0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI2_IRQHandler                         0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI3_IRQHandler                         0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI4_IRQHandler                         0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI9_5_IRQHandler                       0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    FLASH_IRQHandler                         0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_ER_IRQHandler                       0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_EV_IRQHandler                       0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_ER_IRQHandler                       0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_EV_IRQHandler                       0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    PVD_IRQHandler                           0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_Alarm_IRQHandler                     0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_IRQHandler                           0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI1_IRQHandler                          0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI2_IRQHandler                          0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    TAMPER_IRQHandler                        0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_BRK_IRQHandler                      0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_UP_IRQHandler                       0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM2_IRQHandler                          0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM3_IRQHandler                          0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    USBWakeUp_IRQHandler                     0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    WWDG_IRQHandler                          0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    __aeabi_ldivmod                          0x08000129   Thumb Code    98  ldiv.o(.text)
    __aeabi_llsr                             0x0800018b   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x0800018b   Thumb Code     0  llushr.o(.text)
    __aeabi_memcpy                           0x080001ab   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x080001ab   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x080001ab   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x080001cf   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x080001cf   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x080001cf   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x080001dd   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x080001dd   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x080001dd   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x080001e1   Thumb Code    18  memseta.o(.text)
    __aeabi_fadd                             0x080001f3   Thumb Code   164  fadd.o(.text)
    __aeabi_fsub                             0x08000297   Thumb Code     6  fadd.o(.text)
    __aeabi_frsub                            0x0800029d   Thumb Code     6  fadd.o(.text)
    __aeabi_fmul                             0x080002a3   Thumb Code   100  fmul.o(.text)
    __aeabi_fdiv                             0x08000307   Thumb Code   124  fdiv.o(.text)
    __aeabi_i2f                              0x08000383   Thumb Code    18  fflti.o(.text)
    __aeabi_l2d                              0x08000395   Thumb Code    40  dfltl.o(.text)
    __aeabi_f2iz                             0x080003bd   Thumb Code    50  ffixi.o(.text)
    __aeabi_d2lz                             0x080003ef   Thumb Code    74  dfixl.o(.text)
    __aeabi_d2f                              0x08000439   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x08000471   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000471   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x0800049d   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x080004ff   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080004ff   Thumb Code     0  llshl.o(.text)
    __strtod_int                             0x08000553   Thumb Code    90  strtod.o(.text)
    __I$use$fp                               0x080005b9   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x080005b9   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x080005cb   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x08000627   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000645   Thumb Code   156  depilogue.o(.text)
    __aeabi_dadd                             0x080006e1   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000823   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000829   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x0800082f   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000913   Thumb Code   222  ddiv.o(.text)
    _dsqrt                                   0x080009f1   Thumb Code   162  dsqrt.o(.text)
    __aeabi_d2ulz                            0x08000a93   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000ac5   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08000af5   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000af5   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x08000b19   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000b19   Thumb Code     0  llsshr.o(.text)
    isspace                                  0x08000b3d   Thumb Code    10  isspace_c.o(.text)
    _scanf_real                              0x08000c71   Thumb Code     0  scanf_fp.o(.text)
    _scanf_really_real                       0x08000c71   Thumb Code   556  scanf_fp.o(.text)
    _sgetc                                   0x08000ea9   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08000ec7   Thumb Code    34  _sgetc.o(.text)
    __ctype_lookup                           0x08000ee9   Thumb Code    34  ctype_c.o(.text)
    __aeabi_ul2d                             0x08000f11   Thumb Code    24  dfltul.o(.text)
    __decompress                             0x08000f29   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x08000f29   Thumb Code    86  __dczerorl2.o(.text)
    ADC1_2_IRQHandler                        0x08000f81   Thumb Code    18  stm32f1xx_it.o(i.ADC1_2_IRQHandler)
    ADC_ConversionStop_Disable               0x08000f9d   Thumb Code    92  stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable)
    ADC_Enable                               0x08000ff9   Thumb Code   128  stm32f1xx_hal_adc.o(i.ADC_Enable)
    Acce_distance_cal                        0x08001081   Thumb Code    64  motion_control.o(i.Acce_distance_cal)
    BusFault_Handler                         0x080010c5   Thumb Code     2  stm32f1xx_it.o(i.BusFault_Handler)
    CAN_User_Init                            0x080010c9   Thumb Code   120  canopen.o(i.CAN_User_Init)
    Calibrate_ADC_Offset                     0x08001149   Thumb Code   146  mcpwm.o(i.Calibrate_ADC_Offset)
    Check_DCBus                              0x080011f1   Thumb Code   106  position_loop.o(i.Check_DCBus)
    Check_IIt                                0x08001275   Thumb Code    76  position_loop.o(i.Check_IIt)
    Check_Temperature                        0x080012e1   Thumb Code    28  position_loop.o(i.Check_Temperature)
    Current_loop                             0x08001309   Thumb Code   562  current_loop.o(i.Current_loop)
    DMA1_Channel2_IRQHandler                 0x08001565   Thumb Code     6  stm32f1xx_it.o(i.DMA1_Channel2_IRQHandler)
    DMA1_Channel3_IRQHandler                 0x08001571   Thumb Code     6  stm32f1xx_it.o(i.DMA1_Channel3_IRQHandler)
    DMA1_Channel5_IRQHandler                 0x0800157d   Thumb Code     6  stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler)
    DMA1_Channel6_IRQHandler                 0x08001589   Thumb Code     6  stm32f1xx_it.o(i.DMA1_Channel6_IRQHandler)
    DMA1_Channel7_IRQHandler                 0x08001595   Thumb Code     6  stm32f1xx_it.o(i.DMA1_Channel7_IRQHandler)
    DS402_process                            0x080015d9   Thumb Code   216  ds402.o(i.DS402_process)
    DebugMon_Handler                         0x080016e5   Thumb Code     2  stm32f1xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x080016e7   Thumb Code     2  main.o(i.Error_Handler)
    Exchange_motor_code                      0x080016e9   Thumb Code   218  parameter.o(i.Exchange_motor_code)
    Get_Crc16                                0x080017dd   Thumb Code    40  crc_16.o(i.Get_Crc16)
    Get_NTC_Temperature                      0x08001809   Thumb Code    80  ntc_calculate.o(i.Get_NTC_Temperature)
    HAL_ADCEx_InjectedConfigChannel          0x0800185d   Thumb Code   584  stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel)
    HAL_ADCEx_InjectedConvCpltCallback       0x08001aad   Thumb Code     2  stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback)
    HAL_ADCEx_InjectedGetValue               0x08001aaf   Thumb Code    30  stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue)
    HAL_ADCEx_InjectedStart                  0x08001acd   Thumb Code   176  stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart)
    HAL_ADC_ConfigChannel                    0x08001b85   Thumb Code   288  stm32f1xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x08001cad   Thumb Code     2  stm32f1xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_GetValue                         0x08001caf   Thumb Code     6  stm32f1xx_hal_adc.o(i.HAL_ADC_GetValue)
    HAL_ADC_IRQHandler                       0x08001cb5   Thumb Code   254  stm32f1xx_hal_adc.o(i.HAL_ADC_IRQHandler)
    HAL_ADC_Init                             0x08001db5   Thumb Code   280  stm32f1xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_LevelOutOfWindowCallback         0x08001ed5   Thumb Code     2  stm32f1xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback)
    HAL_ADC_MspInit                          0x08001ed9   Thumb Code   180  adc.o(i.HAL_ADC_MspInit)
    HAL_CAN_ActivateNotification             0x08001fa1   Thumb Code    36  stm32f1xx_hal_can.o(i.HAL_CAN_ActivateNotification)
    HAL_CAN_AddTxMessage                     0x08001fc5   Thumb Code   260  stm32f1xx_hal_can.o(i.HAL_CAN_AddTxMessage)
    HAL_CAN_ConfigFilter                     0x080020c9   Thumb Code   264  stm32f1xx_hal_can.o(i.HAL_CAN_ConfigFilter)
    HAL_CAN_ErrorCallback                    0x080021d1   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_ErrorCallback)
    HAL_CAN_GetRxMessage                     0x080021d3   Thumb Code   364  stm32f1xx_hal_can.o(i.HAL_CAN_GetRxMessage)
    HAL_CAN_IRQHandler                       0x0800233f   Thumb Code   564  stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler)
    HAL_CAN_Init                             0x08002573   Thumb Code   360  stm32f1xx_hal_can.o(i.HAL_CAN_Init)
    HAL_CAN_MspInit                          0x080026dd   Thumb Code   112  can.o(i.HAL_CAN_MspInit)
    HAL_CAN_RxFifo0FullCallback              0x08002759   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback)
    HAL_CAN_RxFifo0MsgPendingCallback        0x0800275d   Thumb Code    54  canopen.o(i.HAL_CAN_RxFifo0MsgPendingCallback)
    HAL_CAN_RxFifo1FullCallback              0x080027a1   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback)
    HAL_CAN_RxFifo1MsgPendingCallback        0x080027a3   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback)
    HAL_CAN_SleepCallback                    0x080027a5   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_SleepCallback)
    HAL_CAN_Start                            0x080027a7   Thumb Code   100  stm32f1xx_hal_can.o(i.HAL_CAN_Start)
    HAL_CAN_TxMailbox0AbortCallback          0x0800280b   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback)
    HAL_CAN_TxMailbox0CompleteCallback       0x0800280d   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback)
    HAL_CAN_TxMailbox1AbortCallback          0x0800280f   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback)
    HAL_CAN_TxMailbox1CompleteCallback       0x08002811   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback)
    HAL_CAN_TxMailbox2AbortCallback          0x08002813   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback)
    HAL_CAN_TxMailbox2CompleteCallback       0x08002815   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback)
    HAL_CAN_WakeUpFromRxMsgCallback          0x08002817   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback)
    HAL_DMA_Abort                            0x08002819   Thumb Code    72  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08002861   Thumb Code   136  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08002905   Thumb Code   338  stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08002a75   Thumb Code   118  stm32f1xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08002af9   Thumb Code   124  stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08002b75   Thumb Code    34  stm32f1xx_hal.o(i.HAL_Delay)
    HAL_FLASH_Lock                           0x08002b9d   Thumb Code    14  stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock)
    HAL_FLASH_Unlock                         0x08002bb1   Thumb Code    32  stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock)
    HAL_GPIO_Init                            0x08002bdd   Thumb Code   496  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_TogglePin                       0x08002dd9   Thumb Code    16  stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x08002de9   Thumb Code    14  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08002df9   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08002e05   Thumb Code    12  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08002e15   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08002e39   Thumb Code    58  stm32f1xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08002e7d   Thumb Code    90  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08002ee1   Thumb Code    28  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08002efd   Thumb Code    98  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08002f65   Thumb Code    30  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCCEx_PeriphCLKConfig                0x08002f89   Thumb Code   290  stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x080030b9   Thumb Code   364  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08003239   Thumb Code     6  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08003245   Thumb Code    22  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08003265   Thumb Code    22  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08003285   Thumb Code    88  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x080032fd   Thumb Code  1068  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SPI_Init                             0x08003729   Thumb Code   180  stm32f1xx_hal_spi.o(i.HAL_SPI_Init)
    HAL_SPI_MspInit                          0x080037dd   Thumb Code    98  spi.o(i.HAL_SPI_MspInit)
    HAL_SYSTICK_CLKSourceConfig              0x0800384d   Thumb Code    28  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig)
    HAL_SYSTICK_Config                       0x08003869   Thumb Code    36  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x08003891   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08003893   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_ConfigBreakDeadTime            0x08003895   Thumb Code   102  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x080038fb   Thumb Code   114  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIMEx_PWMN_Start                     0x0800396d   Thumb Code   170  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start)
    HAL_TIMEx_PWMN_Stop                      0x08003a17   Thumb Code   106  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop)
    HAL_TIM_Base_Init                        0x08003a81   Thumb Code    92  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08003add   Thumb Code    86  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08003b41   Thumb Code    98  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_ConfigClockSource                0x08003ba3   Thumb Code   228  stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_IC_CaptureCallback               0x08003c87   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x08003c89   Thumb Code   372  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x08003dfd   Thumb Code    98  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_ConfigChannel                 0x08003e71   Thumb Code    76  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel)
    HAL_TIM_OC_DelayElapsedCallback          0x08003ebd   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_OC_Init                          0x08003ebf   Thumb Code    92  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init)
    HAL_TIM_OC_MspInit                       0x08003f1b   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit)
    HAL_TIM_PWM_ConfigChannel                0x08003f1d   Thumb Code   210  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08003fef   Thumb Code    92  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x0800404b   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x0800404d   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x08004051   Thumb Code   170  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PWM_Start_IT                     0x08004101   Thumb Code   230  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT)
    HAL_TIM_PWM_Stop                         0x080041ed   Thumb Code   114  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop)
    HAL_TIM_PeriodElapsedCallback            0x0800425f   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08004261   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_RxEventCallback               0x08004263   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x08004265   Thumb Code    88  stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x080042bd   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x080042c1   Thumb Code   506  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080044c1   Thumb Code   100  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08004525   Thumb Code   514  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_DMA                     0x08004751   Thumb Code    46  stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA)
    HAL_UART_Receive_IT                      0x0800477f   Thumb Code    46  stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x080047ad   Thumb Code   358  modbus.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08004955   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08004957   Thumb Code   202  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_Transmit_DMA                    0x08004a21   Thumb Code   138  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA)
    HAL_UART_TxCpltCallback                  0x08004ab9   Thumb Code    42  modbus.o(i.HAL_UART_TxCpltCallback)
    HAL_UART_TxHalfCpltCallback              0x08004af9   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback)
    HardFault_Handler                        0x08004afb   Thumb Code     2  stm32f1xx_it.o(i.HardFault_Handler)
    IIt_DC_filter                            0x08004afd   Thumb Code    42  current_loop.o(i.IIt_DC_filter)
    IIt_filter                               0x08004b2d   Thumb Code    42  current_loop.o(i.IIt_filter)
    Init_Control_Parameter                   0x08004b5d   Thumb Code   196  parameter.o(i.Init_Control_Parameter)
    Init_Driver_State                        0x08004cc5   Thumb Code    22  parameter.o(i.Init_Driver_State)
    Init_Modbus_Addr_List                    0x08004ced   Thumb Code   672  modbus.o(i.Init_Modbus_Addr_List)
    Init_Motor_Parameter                     0x080050c5   Thumb Code    66  parameter.o(i.Init_Motor_Parameter)
    Init_System_Parameter                    0x08005135   Thumb Code    88  parameter.o(i.Init_System_Parameter)
    Init_current_loop_state                  0x080051c5   Thumb Code    18  current_loop.o(i.Init_current_loop_state)
    Init_position_loop_state                 0x080051e1   Thumb Code     8  position_loop.o(i.Init_position_loop_state)
    Init_velocity_loop_state                 0x080051ed   Thumb Code    12  velocity_loop.o(i.Init_velocity_loop_state)
    LED_Process                              0x08005201   Thumb Code   180  motion_control.o(i.LED_Process)
    Low_pass_filter_1                        0x080052cd   Thumb Code    16  velocity_loop.o(i.Low_pass_filter_1)
    MX_ADC1_Init                             0x080052dd   Thumb Code   118  adc.o(i.MX_ADC1_Init)
    MX_ADC2_Init                             0x0800535d   Thumb Code   120  adc.o(i.MX_ADC2_Init)
    MX_CAN_Init                              0x080053dd   Thumb Code    60  can.o(i.MX_CAN_Init)
    MX_DMA_Init                              0x08005421   Thumb Code   104  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x0800548d   Thumb Code   226  gpio.o(i.MX_GPIO_Init)
    MX_SPI1_Init                             0x08005581   Thumb Code    68  spi.o(i.MX_SPI1_Init)
    MX_TIM1_Init                             0x080055cd   Thumb Code   264  tim.o(i.MX_TIM1_Init)
    MX_TIM4_Init                             0x080056dd   Thumb Code    96  tim.o(i.MX_TIM4_Init)
    MX_USART1_UART_Init                      0x08005745   Thumb Code    46  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08005781   Thumb Code    48  usart.o(i.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x080057b9   Thumb Code    48  usart.o(i.MX_USART3_UART_Init)
    MemManage_Handler                        0x080057f1   Thumb Code     2  stm32f1xx_it.o(i.MemManage_Handler)
    MemReadModbus                            0x080057f5   Thumb Code    48  parameter.o(i.MemReadModbus)
    Modbus_03_Solve                          0x0800582d   Thumb Code   150  modbus.o(i.Modbus_03_Solve)
    Modbus_06_Solve                          0x080058d1   Thumb Code   100  modbus.o(i.Modbus_06_Solve)
    Modbus_16_Solve                          0x08005941   Thumb Code   170  modbus.o(i.Modbus_16_Solve)
    Modbus_Solve_485_Disenable               0x080059f9   Thumb Code    10  modbus.o(i.Modbus_Solve_485_Disenable)
    Modbus_Solve_485_Enable                  0x08005a09   Thumb Code    10  modbus.o(i.Modbus_Solve_485_Enable)
    Modbus_Solve_Service                     0x08005a19   Thumb Code   150  modbus.o(i.Modbus_Solve_Service)
    Motion_process                           0x08005abd   Thumb Code   828  motion_control.o(i.Motion_process)
    NMI_Handler                              0x08005e25   Thumb Code     2  stm32f1xx_it.o(i.NMI_Handler)
    OC4_PWM_Override                         0x08005e27   Thumb Code    36  tim.o(i.OC4_PWM_Override)
    PendSV_Handler                           0x08005e4b   Thumb Code     2  stm32f1xx_it.o(i.PendSV_Handler)
    Position_Loop                            0x08005e4d   Thumb Code   260  position_loop.o(i.Position_Loop)
    Process_RPDO                             0x08005f75   Thumb Code   158  canopen.o(i.Process_RPDO)
    Process_TPDO                             0x080060e9   Thumb Code    88  canopen.o(i.Process_TPDO)
    RCC_IRQHandler                           0x08006191   Thumb Code     2  stm32f1xx_it.o(i.RCC_IRQHandler)
    ReadValue                                0x08006195   Thumb Code    78  mt6825.o(i.ReadValue)
    Read_OD                                  0x080061ed   Thumb Code    50  dic.o(i.Read_OD)
    SDO_Process                              0x08006225   Thumb Code   300  canopen.o(i.SDO_Process)
    SPIx_ReadWriteByte                       0x08006385   Thumb Code    50  mt6825.o(i.SPIx_ReadWriteByte)
    SVC_Handler                              0x080063bd   Thumb Code     2  stm32f1xx_it.o(i.SVC_Handler)
    SVM                                      0x080063bf   Thumb Code   330  utils.o(i.SVM)
    Search_OD                                0x08006509   Thumb Code    48  dic.o(i.Search_OD)
    SysTick_Handler                          0x0800653d   Thumb Code    14  stm32f1xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x0800654b   Thumb Code    96  main.o(i.SystemClock_Config)
    SystemInit                               0x080065ab   Thumb Code     2  system_stm32f1xx.o(i.SystemInit)
    TIM1_CC_IRQHandler                       0x080065ad   Thumb Code   380  stm32f1xx_it.o(i.TIM1_CC_IRQHandler)
    TIM4_IRQHandler                          0x08006799   Thumb Code    58  stm32f1xx_it.o(i.TIM4_IRQHandler)
    TIM_Base_SetConfig                       0x080067e9   Thumb Code   106  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x08006861   Thumb Code    30  stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_ETR_SetConfig                        0x0800689d   Thumb Code    22  stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x0800692d   Thumb Code   100  stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig)
    Tamagawa_Read_Cmd                        0x08006a95   Thumb Code   120  tamagawa.o(i.Tamagawa_Read_Cmd)
    UART_Start_Receive_DMA                   0x08006e11   Thumb Code   102  stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
    UART_Start_Receive_IT                    0x08006e85   Thumb Code    54  stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x08006f91   Thumb Code     6  stm32f1xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08006f9d   Thumb Code   136  stm32f1xx_it.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08007041   Thumb Code   104  stm32f1xx_it.o(i.USART3_IRQHandler)
    USB_LP_CAN1_RX0_IRQHandler               0x080070c5   Thumb Code     6  stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler)
    Update_Speed                             0x080070d1   Thumb Code   136  velocity_loop.o(i.Update_Speed)
    UsageFault_Handler                       0x08007171   Thumb Code     2  stm32f1xx_it.o(i.UsageFault_Handler)
    Velocity_loop                            0x08007175   Thumb Code   308  velocity_loop.o(i.Velocity_loop)
    Write_OD                                 0x080072c5   Thumb Code    50  dic.o(i.Write_OD)
    __0printf                                0x080072fd   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x080072fd   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x080072fd   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x080072fd   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x080072fd   Thumb Code     0  printfa.o(i.__0printf)
    __read_errno                             0x0800731d   Thumb Code     6  errno.o(i.__read_errno)
    __scatterload_copy                       0x08007329   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08007337   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08007339   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x08007349   Thumb Code     6  errno.o(i.__set_errno)
    _is_digit                                0x080074d9   Thumb Code    14  scanf_fp.o(i._is_digit)
    arm_cos_f32                              0x08007bf1   Thumb Code    52  sin_table.o(i.arm_cos_f32)
    arm_sin_f32                              0x08007c2d   Thumb Code    48  sin_table.o(i.arm_sin_f32)
    atof                                     0x08007c65   Thumb Code    42  atof.o(i.atof)
    delay_init                               0x08007c91   Thumb Code    16  delay.o(i.delay_init)
    delay_ms                                 0x08007ca5   Thumb Code     4  delay.o(i.delay_ms)
    fdummy                                   0x08007ca9   Thumb Code     2  dic.o(i.fdummy)
    find_commutation                         0x08007cad   Thumb Code   430  mcpwm.o(i.find_commutation)
    fputc                                    0x08007e71   Thumb Code    20  vofa_function.o(i.fputc)
    get_electric_phase                       0x08007e89   Thumb Code    32  mcpwm.o(i.get_electric_phase)
    init_motor_control                       0x08007ead   Thumb Code    98  mcpwm.o(i.init_motor_control)
    main                                     0x08007f25   Thumb Code   232  main.o(i.main)
    phase_current_from_adcval                0x08008051   Thumb Code    26  mcpwm.o(i.phase_current_from_adcval)
    queue_modulation_timings                 0x08008071   Thumb Code    72  mcpwm.o(i.queue_modulation_timings)
    sqrt                                     0x080080bd   Thumb Code    76  sqrt.o(i.sqrt)
    start_adc                                0x08008109   Thumb Code    94  mcpwm.o(i.start_adc)
    start_pwm                                0x08008179   Thumb Code    72  mcpwm.o(i.start_pwm)
    stop_pwm                                 0x080081c1   Thumb Code    72  mcpwm.o(i.stop_pwm)
    uart1_init                               0x08008209   Thumb Code    44  tamagawa.o(i.uart1_init)
    uart2_init                               0x08008241   Thumb Code    66  modbus.o(i.uart2_init)
    uart3_init                               0x08008295   Thumb Code    54  modbus.o(i.uart3_init)
    uartCMDRecv                              0x080082d9   Thumb Code    56  vofa_function.o(i.uartCMDRecv)
    update_motor                             0x08008319   Thumb Code   210  mcpwm.o(i.update_motor)
    vofaCommandParse                         0x080083fd   Thumb Code   352  vofa_function.o(i.vofaCommandParse)
    vofaJustFloatInit                        0x0800858d   Thumb Code    28  vofa_function.o(i.vofaJustFloatInit)
    auchCRCHi                                0x080085ac   Data         256  crc_16.o(.constdata)
    auchCRCLo                                0x080086ac   Data         256  crc_16.o(.constdata)
    OD_attribute                             0x080087ac   Data        1036  dic.o(.constdata)
    Sin_Table                                0x08008bb8   Data        4096  sin_table.o(.constdata)
    AHBPrescTable                            0x08009bb8   Data          16  system_stm32f1xx.o(.constdata)
    APBPrescTable                            0x08009bc8   Data           8  system_stm32f1xx.o(.constdata)
    __ctype_categories                       0x08009bd0   Data          64  ctype_c.o(.constdata)
    Region$$Table$$Base                      0x08009c10   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08009c30   Number         0  anon$$obj.o(Region$$Table)
    CAN_Baudrate                             0x20000000   Data           2  canopen.o(.data)
    CAN_Rx_Data                              0x20000002   Data           8  canopen.o(.data)
    CAN_Tx_Data                              0x2000000a   Data           8  canopen.o(.data)
    TPDO1_Data                               0x20000012   Data           8  canopen.o(.data)
    Error_register                           0x2000001a   Data           1  canopen.o(.data)
    TPDO_Period                              0x2000001c   Data           2  canopen.o(.data)
    tim_count                                0x2000001e   Data           2  canopen.o(.data)
    current_out_lpf_a                        0x20000020   Data           2  current_loop.o(.data)
    vfactor                                  0x20000024   Data           4  current_loop.o(.data)
    V_current_control_integral_d             0x20000028   Data           4  current_loop.o(.data)
    V_current_control_integral_q             0x2000002c   Data           4  current_loop.o(.data)
    Vq_out_limit                             0x20000030   Data           4  current_loop.o(.data)
    Vd_out_limit                             0x20000034   Data           4  current_loop.o(.data)
    kci_sum_limit                            0x20000038   Data           4  current_loop.o(.data)
    kcp                                      0x2000003c   Data           4  current_loop.o(.data)
    kci                                      0x20000040   Data           4  current_loop.o(.data)
    check_current_overshot_p                 0x20000044   Data           4  current_loop.o(.data)
    check_current_overshot_n                 0x20000048   Data           4  current_loop.o(.data)
    IIt_Remaider                             0x2000004c   Data           4  current_loop.o(.data)
    IIt_temp                                 0x20000050   Data           4  current_loop.o(.data)
    IIt_DC_Remaider                          0x20000054   Data           4  current_loop.o(.data)
    IIt_DC_temp                              0x20000058   Data           4  current_loop.o(.data)
    Ialpha                                   0x2000005c   Data           4  current_loop.o(.data)
    Ibeta                                    0x20000060   Data           4  current_loop.o(.data)
    Ierr_d                                   0x20000064   Data           4  current_loop.o(.data)
    Ierr_q                                   0x20000068   Data           4  current_loop.o(.data)
    Vd                                       0x2000006c   Data           4  current_loop.o(.data)
    Vd_filter                                0x20000070   Data           4  current_loop.o(.data)
    Vq                                       0x20000074   Data           4  current_loop.o(.data)
    Vq_filter                                0x20000078   Data           4  current_loop.o(.data)
    mod_d                                    0x2000007c   Data           4  current_loop.o(.data)
    mod_q                                    0x20000080   Data           4  current_loop.o(.data)
    mod_alpha                                0x20000084   Data           4  current_loop.o(.data)
    mod_beta                                 0x20000088   Data           4  current_loop.o(.data)
    current_in_lpf_a                         0x2000008c   Data           2  current_loop.o(.data)
    Driver_IIt_Real                          0x20000090   Data           4  current_loop.o(.data)
    Driver_IIt_Real_DC                       0x20000094   Data           4  current_loop.o(.data)
    Driver_IIt_Current                       0x20000098   Data           4  current_loop.o(.data)
    Driver_IIt_Current_DC                    0x2000009c   Data           4  current_loop.o(.data)
    Driver_IIt_Filter                        0x200000a0   Data           2  current_loop.o(.data)
    Driver_IIt_Filter_DC                     0x200000a2   Data           2  current_loop.o(.data)
    OD_Num                                   0x200000a8   Data           4  dic.o(.data)
    Write_Access                             0x200000ac   Data           1  dic.o(.data)
    VCP_RX_Len                               0x200000ae   Data           2  dic.o(.data)
    VCP_TX_Buffer                            0x200000b0   Data         100  dic.o(.data)
    operation_mode                           0x20000114   Data           2  ds402.o(.data)
    motor_on                                 0x20000116   Data           2  ds402.o(.data)
    control_word                             0x20000118   Data           2  ds402.o(.data)
    control_word_b                           0x2000011a   Data           2  ds402.o(.data)
    status_word                              0x2000011c   Data           2  ds402.o(.data)
    RS485_FrameFlag                          0x20000120   Data           1  modbus.o(.data)
    RS485_TX_EN                              0x20000121   Data           1  modbus.o(.data)
    RS485_RX_CNT                             0x20000122   Data           1  modbus.o(.data)
    RS232_FrameFlag                          0x20000123   Data           1  modbus.o(.data)
    RS232_TX_EN                              0x20000124   Data           1  modbus.o(.data)
    RS232_RX_CNT                             0x20000125   Data           1  modbus.o(.data)
    USART3RxBuffer                           0x20000126   Data           1  modbus.o(.data)
    USART3_RX_STA                            0x20000128   Data           2  modbus.o(.data)
    USART2_RX_STA                            0x2000012a   Data           2  modbus.o(.data)
    RS232_Addr                               0x2000012c   Data           2  modbus.o(.data)
    RS485_Addr                               0x2000012e   Data           2  modbus.o(.data)
    Modbus_Addr_Base                         0x20000130   Data           2  modbus.o(.data)
    RS485_Protocol                           0x20000132   Data           2  modbus.o(.data)
    startRegAddr                             0x20000134   Data           2  modbus.o(.data)
    Reserve                                  0x20000136   Data           2  modbus.o(.data)
    RegNum                                   0x20000138   Data           2  modbus.o(.data)
    calCRC                                   0x2000013a   Data           2  modbus.o(.data)
    RS485_Baudrate                           0x2000013c   Data           4  modbus.o(.data)
    USART3_RX_TIMECHK                        0x20000140   Data           4  modbus.o(.data)
    software_version                         0x20000144   Data           4  modbus.o(.data)
    RS232_Baudrate                           0x20000148   Data           4  modbus.o(.data)
    __stdout                                 0x2000014c   Data           4  modbus.o(.data)
    accelerating                             0x20000150   Data           1  motion_control.o(.data)
    decelerating                             0x20000151   Data           1  motion_control.o(.data)
    positionning                             0x20000152   Data           1  motion_control.o(.data)
    motion_state                             0x20000153   Data           1  motion_control.o(.data)
    auto_switch_on                           0x20000154   Data           2  motion_control.o(.data)
    profile_acce                             0x20000158   Data           4  motion_control.o(.data)
    profile_dece                             0x2000015c   Data           4  motion_control.o(.data)
    profile_speed                            0x20000160   Data           4  motion_control.o(.data)
    target_speed_now                         0x20000164   Data           4  motion_control.o(.data)
    direction                                0x20000168   Data           4  motion_control.o(.data)
    distance_diff                            0x2000016c   Data           4  motion_control.o(.data)
    target_pos_now                           0x20000170   Data           4  motion_control.o(.data)
    searching_speed                          0x20000174   Data           4  motion_control.o(.data)
    auto_p_pos                               0x20000178   Data           4  motion_control.o(.data)
    auto_n_pos                               0x2000017c   Data           4  motion_control.o(.data)
    auto_reverse_p_time                      0x20000180   Data           4  motion_control.o(.data)
    auto_reverse_n_time                      0x20000184   Data           4  motion_control.o(.data)
    auto_reverse_time                        0x20000188   Data           4  motion_control.o(.data)
    profile_speed_b                          0x2000018c   Data           4  motion_control.o(.data)
    profile_target_position_b                0x20000190   Data           4  motion_control.o(.data)
    acce_diatance                            0x20000194   Data           4  motion_control.o(.data)
    dece_diatance                            0x20000198   Data           4  motion_control.o(.data)
    auto_reverse_status                      0x2000019c   Data           4  motion_control.o(.data)
    remain_dst                               0x200001a0   Data           8  motion_control.o(.data)
    step_dst                                 0x200001a8   Data           8  motion_control.o(.data)
    end_speed                                0x200001b0   Data           4  motion_control.o(.data)
    home_offest                              0x200001b4   Data           4  motion_control.o(.data)
    homing_speed                             0x200001b8   Data           4  motion_control.o(.data)
    homing_acce                              0x200001bc   Data           4  motion_control.o(.data)
    homing_method                            0x200001c0   Data           1  motion_control.o(.data)
    gear_factor_a                            0x200001c4   Data           4  motion_control.o(.data)
    gear_factor_b                            0x200001c8   Data           4  motion_control.o(.data)
    motion_out_lpf_a                         0x200001cc   Data           2  motion_control.o(.data)
    profile_target_position                  0x200001d0   Data           4  motion_control.o(.data)
    NTC_Table                                0x200001d4   Data          84  ntc_calculate.o(.data)
    NTC_R_Value                              0x20000228   Data           4  ntc_calculate.o(.data)
    data_flash_addr                          0x2000022c   Data           4  parameter.o(.data)
    vbus_voltage                             0x20000230   Data           2  mcpwm.o(.data)
    device_temperature                       0x20000232   Data           2  mcpwm.o(.data)
    phase_dir                                0x20000234   Data           2  mcpwm.o(.data)
    hall_phase_dir                           0x20000236   Data           2  mcpwm.o(.data)
    commutation_founded                      0x20000238   Data           2  mcpwm.o(.data)
    commutation_mode                         0x2000023a   Data           2  mcpwm.o(.data)
    commutation_time                         0x2000023c   Data           2  mcpwm.o(.data)
    feedback_type                            0x2000023e   Data           2  mcpwm.o(.data)
    poles_num                                0x20000240   Data           2  mcpwm.o(.data)
    tamagawa_offset                          0x20000242   Data           2  mcpwm.o(.data)
    tamagawa_dir                             0x20000244   Data           2  mcpwm.o(.data)
    Driver_Ready                             0x20000246   Data           2  mcpwm.o(.data)
    ENC_Z_Count                              0x20000248   Data           2  mcpwm.o(.data)
    ENC_Z_Count_B                            0x2000024a   Data           2  mcpwm.o(.data)
    ENC_Z_First                              0x2000024c   Data           2  mcpwm.o(.data)
    hall_state                               0x2000024e   Data           2  mcpwm.o(.data)
    hall_phase_offset                        0x20000250   Data           2  mcpwm.o(.data)
    encoder_offset_diff                      0x20000252   Data           2  mcpwm.o(.data)
    Iq_demand                                0x20000254   Data           4  mcpwm.o(.data)
    commutation_current                      0x20000258   Data           4  mcpwm.o(.data)
    feedback_resolution                      0x2000025c   Data           4  mcpwm.o(.data)
    rad_of_round                             0x20000260   Data           4  mcpwm.o(.data)
    Error_State                              0x20000264   Data           4  mcpwm.o(.data)
    tA                                       0x20000268   Data           4  mcpwm.o(.data)
    tB                                       0x2000026c   Data           4  mcpwm.o(.data)
    tC                                       0x20000270   Data           4  mcpwm.o(.data)
    my_p0                                    0x20000274   Data           4  mcpwm.o(.data)
    my_p1                                    0x20000278   Data           4  mcpwm.o(.data)
    motor                                    0x2000027c   Data          68  mcpwm.o(.data)
    vel_dir                                  0x200002c0   Data           2  mcpwm.o(.data)
    Id_demand                                0x200002c4   Data           4  mcpwm.o(.data)
    target_Iq                                0x200002c8   Data           4  mcpwm.o(.data)
    target_Id                                0x200002cc   Data           4  mcpwm.o(.data)
    target_speed                             0x200002d0   Data           4  mcpwm.o(.data)
    speed_demand                             0x200002d4   Data           4  mcpwm.o(.data)
    target_position                          0x200002d8   Data           4  mcpwm.o(.data)
    motor_rated_current                      0x200002dc   Data           4  mcpwm.o(.data)
    motor_peak_current                       0x200002e0   Data           4  mcpwm.o(.data)
    motor_overload_time                      0x200002e4   Data           4  mcpwm.o(.data)
    loop_counter_c                           0x200002e8   Data           4  mcpwm.o(.data)
    loop_counter_v                           0x200002ec   Data           4  mcpwm.o(.data)
    loop_counter_p                           0x200002f0   Data           4  mcpwm.o(.data)
    current_loop_ready                       0x200002f4   Data           4  mcpwm.o(.data)
    velocity_loop_ready                      0x200002f8   Data           4  mcpwm.o(.data)
    position_loop_ready                      0x200002fc   Data           4  mcpwm.o(.data)
    motor_code                               0x20000300   Data           2  mcpwm.o(.data)
    drv8301_error                            0x20000304   Data           4  mcpwm.o(.data)
    ENC_Counting_Error                       0x20000308   Data           2  mcpwm.o(.data)
    start_calibrate_hall_phase               0x2000030a   Data           2  mcpwm.o(.data)
    ENC_Z_Phase_B                            0x2000030c   Data           2  mcpwm.o(.data)
    led_blink_counter                        0x20000310   Data           4  mcpwm.o(.data)
    led_blink_period                         0x20000314   Data           4  mcpwm.o(.data)
    store_parameter                          0x20000318   Data           2  mcpwm.o(.data)
    Id                                       0x2000031c   Data           4  mcpwm.o(.data)
    Iq                                       0x20000320   Data           4  mcpwm.o(.data)
    Iq_real                                  0x20000324   Data           4  mcpwm.o(.data)
    Id_real                                  0x20000328   Data           4  mcpwm.o(.data)
    position_demand                          0x2000032c   Data           4  mcpwm.o(.data)
    over_voltage                             0x20000330   Data           2  mcpwm.o(.data)
    under_voltage                            0x20000332   Data           2  mcpwm.o(.data)
    chop_voltage                             0x20000334   Data           2  mcpwm.o(.data)
    over_temperature                         0x20000336   Data           2  mcpwm.o(.data)
    position_out_lpf_a                       0x20000338   Data           2  position_loop.o(.data)
    vel_lim                                  0x2000033c   Data           4  position_loop.o(.data)
    kpp                                      0x20000340   Data           4  position_loop.o(.data)
    kpi                                      0x20000344   Data           4  position_loop.o(.data)
    pos_offest                               0x20000348   Data           4  position_loop.o(.data)
    kpi_sum                                  0x2000034c   Data           4  position_loop.o(.data)
    kpi_sum_limit                            0x20000350   Data           4  position_loop.o(.data)
    check_pos_overshot_p                     0x20000354   Data           4  position_loop.o(.data)
    check_pos_overshot_n                     0x20000358   Data           4  position_loop.o(.data)
    pos_actual                               0x2000035c   Data           4  position_loop.o(.data)
    pos_err                                  0x20000360   Data           4  position_loop.o(.data)
    vel_des                                  0x20000364   Data           4  position_loop.o(.data)
    position_in_lpf_a                        0x20000368   Data           2  position_loop.o(.data)
    num                                      0x2000036a   Data           2  sin_table.o(.data)
    low_pass_filter_on                       0x2000036c   Data           2  velocity_loop.o(.data)
    speed_in_lpf_a                           0x2000036e   Data           2  velocity_loop.o(.data)
    speed_out_lpf_a                          0x20000370   Data           2  velocity_loop.o(.data)
    kvi_sum                                  0x20000374   Data           4  velocity_loop.o(.data)
    kvi_sum_limit                            0x20000378   Data           4  velocity_loop.o(.data)
    kvp                                      0x2000037c   Data           4  velocity_loop.o(.data)
    kvi                                      0x20000380   Data           4  velocity_loop.o(.data)
    buff_sum                                 0x20000384   Data           4  velocity_loop.o(.data)
    Iq_temp                                  0x20000388   Data           4  velocity_loop.o(.data)
    Ilim                                     0x2000038c   Data           4  velocity_loop.o(.data)
    Step_phase                               0x20000390   Data           4  velocity_loop.o(.data)
    check_vel_overshot_p                     0x20000394   Data           4  velocity_loop.o(.data)
    check_vel_overshot_n                     0x20000398   Data           4  velocity_loop.o(.data)
    display_speed_loop_count                 0x2000039c   Data           4  velocity_loop.o(.data)
    display_speed                            0x200003a0   Data           4  velocity_loop.o(.data)
    display_encoder_state_b                  0x200003a4   Data           4  velocity_loop.o(.data)
    encoder_state_b                          0x200003a8   Data           4  velocity_loop.o(.data)
    real_speed                               0x200003ac   Data           4  velocity_loop.o(.data)
    real_speed_filter                        0x200003b0   Data           4  velocity_loop.o(.data)
    speed_err                                0x200003b4   Data           4  velocity_loop.o(.data)
    real_speed_buff_point                    0x200003b8   Data           4  velocity_loop.o(.data)
    real_speed_filter_num                    0x200003bc   Data           2  velocity_loop.o(.data)
    spi0_send_array                          0x200003be   Data           6  mt6825.o(.data)
    spi0_receive_array                       0x200003c4   Data           6  mt6825.o(.data)
    tt1                                      0x200003cc   Data           4  stm32f1xx_it.o(.data)
    Tamagawa_FrameFlag                       0x200003d0   Data           1  tamagawa.o(.data)
    Tamagawa_TX_EN                           0x200003d1   Data           1  tamagawa.o(.data)
    Tamagawa_RX_CNT                          0x200003d2   Data           1  tamagawa.o(.data)
    USART1RxBuffer                           0x200003d3   Data           1  tamagawa.o(.data)
    USART1_RX_STA                            0x200003d4   Data           2  tamagawa.o(.data)
    Tamagawa_Baudrate                        0x200003d8   Data           4  tamagawa.o(.data)
    Tamagawa_CRC_count                       0x200003dc   Data           2  tamagawa.o(.data)
    Tamagawa_First                           0x200003de   Data           2  tamagawa.o(.data)
    Tamagawa_count_temp                      0x200003e0   Data           2  tamagawa.o(.data)
    Tamagawa_lost                            0x200003e2   Data           2  tamagawa.o(.data)
    tamagawa_angle                           0x200003e4   Data           2  tamagawa.o(.data)
    tamagawa_angle_b                         0x200003e6   Data           2  tamagawa.o(.data)
    set_tamagawa_zero                        0x200003e8   Data           2  tamagawa.o(.data)
    set_tamagawa_zero_count                  0x200003ea   Data           2  tamagawa.o(.data)
    tamagawa_angle_32                        0x200003ec   Data           4  tamagawa.o(.data)
    tamagawa_ENID                            0x200003f0   Data           4  tamagawa.o(.data)
    tamagawa_ALMC                            0x200003f4   Data           4  tamagawa.o(.data)
    tamagawa_multi_turn                      0x200003f8   Data           2  tamagawa.o(.data)
    tamagawa_angle_4                         0x200003fa   Data           2  tamagawa.o(.data)
    Tamagawa_calCRC                          0x200003fc   Data           2  tamagawa.o(.data)
    vofaRxBufferIndex                        0x200003fe   Data           1  vofa_function.o(.data)
    uwTickFreq                               0x20000400   Data           1  stm32f1xx_hal.o(.data)
    uwTickPrio                               0x20000404   Data           4  stm32f1xx_hal.o(.data)
    uwTick                                   0x20000408   Data           4  stm32f1xx_hal.o(.data)
    SystemCoreClock                          0x2000040c   Data           4  system_stm32f1xx.o(.data)
    TxMessage1                               0x20000414   Data          24  canopen.o(.bss)
    TxMeg                                    0x2000042c   Data          24  canopen.o(.bss)
    RxMeg                                    0x20000444   Data          28  canopen.o(.bss)
    SDO_Access_OBJ                           0x20000460   Data          28  canopen.o(.bss)
    RS485_RX_BUFF                            0x2000047c   Data         200  modbus.o(.bss)
    RS485_TX_BUFF                            0x20000544   Data         200  modbus.o(.bss)
    RS232_RX_BUFF                            0x2000060c   Data         200  modbus.o(.bss)
    RS232_TX_BUFF                            0x200006d4   Data         200  modbus.o(.bss)
    Modbus_Output_Reg                        0x2000079c   Data        2000  modbus.o(.bss)
    ADCValue                                 0x20000f6c   Data          16  mcpwm.o(.bss)
    ADC_Offset                               0x20000f7c   Data          16  mcpwm.o(.bss)
    hall_phase                               0x20000f8c   Data          16  mcpwm.o(.bss)
    hadc1                                    0x20000f9c   Data          48  adc.o(.bss)
    hadc2                                    0x20000fcc   Data          48  adc.o(.bss)
    hcan                                     0x20000ffc   Data          40  can.o(.bss)
    hspi1                                    0x20001024   Data          88  spi.o(.bss)
    htim1                                    0x2000107c   Data          72  tim.o(.bss)
    htim4                                    0x200010c4   Data          72  tim.o(.bss)
    huart1                                   0x2000110c   Data          68  usart.o(.bss)
    huart2                                   0x20001150   Data          68  usart.o(.bss)
    huart3                                   0x20001194   Data          68  usart.o(.bss)
    hdma_usart1_rx                           0x200011d8   Data          68  usart.o(.bss)
    hdma_usart2_rx                           0x2000121c   Data          68  usart.o(.bss)
    hdma_usart2_tx                           0x20001260   Data          68  usart.o(.bss)
    hdma_usart3_rx                           0x200012a4   Data          68  usart.o(.bss)
    hdma_usart3_tx                           0x200012e8   Data          68  usart.o(.bss)
    Tamagawa_RX_BUFF                         0x2000132c   Data         200  tamagawa.o(.bss)
    Tamagawa_TX_BUFF                         0x200013f4   Data         200  tamagawa.o(.bss)
    JustFloat_Data                           0x200015bc   Data          16  vofa_function.o(.bss)
    vofaCommandData                          0x200015cc   Data          28  vofa_function.o(.bss)
    vofaTxBuffer                             0x200015e8   Data          16  vofa_function.o(.bss)
    __initial_sp                             0x200019f8   Data           0  startup_stm32f103xb.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000a044, Max: 0x00010000, ABSOLUTE, COMPRESSED[0x00009d34])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00009c30, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f103xb.o
    0x080000ec   0x080000ec   0x00000000   Code   RO         5325  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080000ec   0x080000ec   0x00000004   Code   RO         5619    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080000f0   0x080000f0   0x00000004   Code   RO         5622    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080000f4   0x080000f4   0x00000000   Code   RO         5624    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080000f4   0x080000f4   0x00000000   Code   RO         5626    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080000f4   0x080000f4   0x00000008   Code   RO         5627    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080000fc   0x080000fc   0x00000004   Code   RO         5634    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x08000100   0x08000100   0x00000000   Code   RO         5629    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x08000100   0x08000100   0x00000000   Code   RO         5631    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x08000100   0x08000100   0x00000004   Code   RO         5620    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000104   0x08000104   0x00000024   Code   RO            4    .text               startup_stm32f103xb.o
    0x08000128   0x08000128   0x00000062   Code   RO         5328    .text               mc_w.l(ldiv.o)
    0x0800018a   0x0800018a   0x00000020   Code   RO         5330    .text               mc_w.l(llushr.o)
    0x080001aa   0x080001aa   0x00000024   Code   RO         5332    .text               mc_w.l(memcpya.o)
    0x080001ce   0x080001ce   0x00000024   Code   RO         5334    .text               mc_w.l(memseta.o)
    0x080001f2   0x080001f2   0x000000b0   Code   RO         5599    .text               mf_w.l(fadd.o)
    0x080002a2   0x080002a2   0x00000064   Code   RO         5601    .text               mf_w.l(fmul.o)
    0x08000306   0x08000306   0x0000007c   Code   RO         5603    .text               mf_w.l(fdiv.o)
    0x08000382   0x08000382   0x00000012   Code   RO         5605    .text               mf_w.l(fflti.o)
    0x08000394   0x08000394   0x00000028   Code   RO         5609    .text               mf_w.l(dfltl.o)
    0x080003bc   0x080003bc   0x00000032   Code   RO         5611    .text               mf_w.l(ffixi.o)
    0x080003ee   0x080003ee   0x0000004a   Code   RO         5613    .text               mf_w.l(dfixl.o)
    0x08000438   0x08000438   0x00000038   Code   RO         5617    .text               mf_w.l(d2f.o)
    0x08000470   0x08000470   0x0000002c   Code   RO         5635    .text               mc_w.l(uidiv.o)
    0x0800049c   0x0800049c   0x00000062   Code   RO         5637    .text               mc_w.l(uldiv.o)
    0x080004fe   0x080004fe   0x0000001e   Code   RO         5639    .text               mc_w.l(llshl.o)
    0x0800051c   0x0800051c   0x0000009c   Code   RO         5648    .text               mc_w.l(strtod.o)
    0x080005b8   0x080005b8   0x00000000   Code   RO         5650    .text               mc_w.l(iusefp.o)
    0x080005b8   0x080005b8   0x0000006e   Code   RO         5651    .text               mf_w.l(fepilogue.o)
    0x08000626   0x08000626   0x000000ba   Code   RO         5653    .text               mf_w.l(depilogue.o)
    0x080006e0   0x080006e0   0x0000014e   Code   RO         5655    .text               mf_w.l(dadd.o)
    0x0800082e   0x0800082e   0x000000e4   Code   RO         5657    .text               mf_w.l(dmul.o)
    0x08000912   0x08000912   0x000000de   Code   RO         5659    .text               mf_w.l(ddiv.o)
    0x080009f0   0x080009f0   0x000000a2   Code   RO         5661    .text               mf_w.l(dsqrt.o)
    0x08000a92   0x08000a92   0x00000030   Code   RO         5663    .text               mf_w.l(dfixul.o)
    0x08000ac2   0x08000ac2   0x00000002   PAD
    0x08000ac4   0x08000ac4   0x00000030   Code   RO         5667    .text               mf_w.l(cdrcmple.o)
    0x08000af4   0x08000af4   0x00000024   Code   RO         5669    .text               mc_w.l(init.o)
    0x08000b18   0x08000b18   0x00000024   Code   RO         5671    .text               mc_w.l(llsshr.o)
    0x08000b3c   0x08000b3c   0x0000000a   Code   RO         5673    .text               mc_w.l(isspace_c.o)
    0x08000b46   0x08000b46   0x00000002   PAD
    0x08000b48   0x08000b48   0x00000360   Code   RO         5675    .text               mc_w.l(scanf_fp.o)
    0x08000ea8   0x08000ea8   0x00000040   Code   RO         5679    .text               mc_w.l(_sgetc.o)
    0x08000ee8   0x08000ee8   0x00000028   Code   RO         5681    .text               mc_w.l(ctype_c.o)
    0x08000f10   0x08000f10   0x00000018   Code   RO         5684    .text               mf_w.l(dfltul.o)
    0x08000f28   0x08000f28   0x00000056   Code   RO         5694    .text               mc_w.l(__dczerorl2.o)
    0x08000f7e   0x08000f7e   0x00000002   PAD
    0x08000f80   0x08000f80   0x0000001c   Code   RO         1654    i.ADC1_2_IRQHandler  stm32f1xx_it.o
    0x08000f9c   0x08000f9c   0x0000005c   Code   RO         2099    i.ADC_ConversionStop_Disable  stm32f1xx_hal_adc.o
    0x08000ff8   0x08000ff8   0x00000088   Code   RO         2103    i.ADC_Enable        stm32f1xx_hal_adc.o
    0x08001080   0x08001080   0x00000044   Code   RO          710    i.Acce_distance_cal  motion_control.o
    0x080010c4   0x080010c4   0x00000002   Code   RO         1655    i.BusFault_Handler  stm32f1xx_it.o
    0x080010c6   0x080010c6   0x00000002   PAD
    0x080010c8   0x080010c8   0x00000080   Code   RO           13    i.CAN_User_Init     canopen.o
    0x08001148   0x08001148   0x000000a8   Code   RO          911    i.Calibrate_ADC_Offset  mcpwm.o
    0x080011f0   0x080011f0   0x00000084   Code   RO         1076    i.Check_DCBus       position_loop.o
    0x08001274   0x08001274   0x0000006c   Code   RO         1077    i.Check_IIt         position_loop.o
    0x080012e0   0x080012e0   0x00000028   Code   RO         1078    i.Check_Temperature  position_loop.o
    0x08001308   0x08001308   0x0000025c   Code   RO          246    i.Current_loop      current_loop.o
    0x08001564   0x08001564   0x0000000c   Code   RO         1656    i.DMA1_Channel2_IRQHandler  stm32f1xx_it.o
    0x08001570   0x08001570   0x0000000c   Code   RO         1657    i.DMA1_Channel3_IRQHandler  stm32f1xx_it.o
    0x0800157c   0x0800157c   0x0000000c   Code   RO         1658    i.DMA1_Channel5_IRQHandler  stm32f1xx_it.o
    0x08001588   0x08001588   0x0000000c   Code   RO         1659    i.DMA1_Channel6_IRQHandler  stm32f1xx_it.o
    0x08001594   0x08001594   0x0000000c   Code   RO         1660    i.DMA1_Channel7_IRQHandler  stm32f1xx_it.o
    0x080015a0   0x080015a0   0x00000038   Code   RO         2758    i.DMA_SetConfig     stm32f1xx_hal_dma.o
    0x080015d8   0x080015d8   0x0000010c   Code   RO          516    i.DS402_process     ds402.o
    0x080016e4   0x080016e4   0x00000002   Code   RO         1661    i.DebugMon_Handler  stm32f1xx_it.o
    0x080016e6   0x080016e6   0x00000002   Code   RO         1315    i.Error_Handler     main.o
    0x080016e8   0x080016e8   0x000000f4   Code   RO          830    i.Exchange_motor_code  parameter.o
    0x080017dc   0x080017dc   0x0000002c   Code   RO          216    i.Get_Crc16         crc_16.o
    0x08001808   0x08001808   0x00000054   Code   RO          799    i.Get_NTC_Temperature  ntc_calculate.o
    0x0800185c   0x0800185c   0x00000250   Code   RO         2280    i.HAL_ADCEx_InjectedConfigChannel  stm32f1xx_hal_adc_ex.o
    0x08001aac   0x08001aac   0x00000002   Code   RO         2281    i.HAL_ADCEx_InjectedConvCpltCallback  stm32f1xx_hal_adc_ex.o
    0x08001aae   0x08001aae   0x0000001e   Code   RO         2282    i.HAL_ADCEx_InjectedGetValue  stm32f1xx_hal_adc_ex.o
    0x08001acc   0x08001acc   0x000000b8   Code   RO         2284    i.HAL_ADCEx_InjectedStart  stm32f1xx_hal_adc_ex.o
    0x08001b84   0x08001b84   0x00000128   Code   RO         2105    i.HAL_ADC_ConfigChannel  stm32f1xx_hal_adc.o
    0x08001cac   0x08001cac   0x00000002   Code   RO         2106    i.HAL_ADC_ConvCpltCallback  stm32f1xx_hal_adc.o
    0x08001cae   0x08001cae   0x00000006   Code   RO         2112    i.HAL_ADC_GetValue  stm32f1xx_hal_adc.o
    0x08001cb4   0x08001cb4   0x000000fe   Code   RO         2113    i.HAL_ADC_IRQHandler  stm32f1xx_hal_adc.o
    0x08001db2   0x08001db2   0x00000002   PAD
    0x08001db4   0x08001db4   0x00000120   Code   RO         2114    i.HAL_ADC_Init      stm32f1xx_hal_adc.o
    0x08001ed4   0x08001ed4   0x00000002   Code   RO         2115    i.HAL_ADC_LevelOutOfWindowCallback  stm32f1xx_hal_adc.o
    0x08001ed6   0x08001ed6   0x00000002   PAD
    0x08001ed8   0x08001ed8   0x000000c8   Code   RO         1385    i.HAL_ADC_MspInit   adc.o
    0x08001fa0   0x08001fa0   0x00000024   Code   RO         3372    i.HAL_CAN_ActivateNotification  stm32f1xx_hal_can.o
    0x08001fc4   0x08001fc4   0x00000104   Code   RO         3373    i.HAL_CAN_AddTxMessage  stm32f1xx_hal_can.o
    0x080020c8   0x080020c8   0x00000108   Code   RO         3374    i.HAL_CAN_ConfigFilter  stm32f1xx_hal_can.o
    0x080021d0   0x080021d0   0x00000002   Code   RO         3377    i.HAL_CAN_ErrorCallback  stm32f1xx_hal_can.o
    0x080021d2   0x080021d2   0x0000016c   Code   RO         3380    i.HAL_CAN_GetRxMessage  stm32f1xx_hal_can.o
    0x0800233e   0x0800233e   0x00000234   Code   RO         3384    i.HAL_CAN_IRQHandler  stm32f1xx_hal_can.o
    0x08002572   0x08002572   0x00000168   Code   RO         3385    i.HAL_CAN_Init      stm32f1xx_hal_can.o
    0x080026da   0x080026da   0x00000002   PAD
    0x080026dc   0x080026dc   0x0000007c   Code   RO         1433    i.HAL_CAN_MspInit   can.o
    0x08002758   0x08002758   0x00000002   Code   RO         3392    i.HAL_CAN_RxFifo0FullCallback  stm32f1xx_hal_can.o
    0x0800275a   0x0800275a   0x00000002   PAD
    0x0800275c   0x0800275c   0x00000044   Code   RO           14    i.HAL_CAN_RxFifo0MsgPendingCallback  canopen.o
    0x080027a0   0x080027a0   0x00000002   Code   RO         3394    i.HAL_CAN_RxFifo1FullCallback  stm32f1xx_hal_can.o
    0x080027a2   0x080027a2   0x00000002   Code   RO         3395    i.HAL_CAN_RxFifo1MsgPendingCallback  stm32f1xx_hal_can.o
    0x080027a4   0x080027a4   0x00000002   Code   RO         3396    i.HAL_CAN_SleepCallback  stm32f1xx_hal_can.o
    0x080027a6   0x080027a6   0x00000064   Code   RO         3397    i.HAL_CAN_Start     stm32f1xx_hal_can.o
    0x0800280a   0x0800280a   0x00000002   Code   RO         3399    i.HAL_CAN_TxMailbox0AbortCallback  stm32f1xx_hal_can.o
    0x0800280c   0x0800280c   0x00000002   Code   RO         3400    i.HAL_CAN_TxMailbox0CompleteCallback  stm32f1xx_hal_can.o
    0x0800280e   0x0800280e   0x00000002   Code   RO         3401    i.HAL_CAN_TxMailbox1AbortCallback  stm32f1xx_hal_can.o
    0x08002810   0x08002810   0x00000002   Code   RO         3402    i.HAL_CAN_TxMailbox1CompleteCallback  stm32f1xx_hal_can.o
    0x08002812   0x08002812   0x00000002   Code   RO         3403    i.HAL_CAN_TxMailbox2AbortCallback  stm32f1xx_hal_can.o
    0x08002814   0x08002814   0x00000002   Code   RO         3404    i.HAL_CAN_TxMailbox2CompleteCallback  stm32f1xx_hal_can.o
    0x08002816   0x08002816   0x00000002   Code   RO         3406    i.HAL_CAN_WakeUpFromRxMsgCallback  stm32f1xx_hal_can.o
    0x08002818   0x08002818   0x00000048   Code   RO         2759    i.HAL_DMA_Abort     stm32f1xx_hal_dma.o
    0x08002860   0x08002860   0x000000a4   Code   RO         2760    i.HAL_DMA_Abort_IT  stm32f1xx_hal_dma.o
    0x08002904   0x08002904   0x00000170   Code   RO         2764    i.HAL_DMA_IRQHandler  stm32f1xx_hal_dma.o
    0x08002a74   0x08002a74   0x00000084   Code   RO         2765    i.HAL_DMA_Init      stm32f1xx_hal_dma.o
    0x08002af8   0x08002af8   0x0000007c   Code   RO         2769    i.HAL_DMA_Start_IT  stm32f1xx_hal_dma.o
    0x08002b74   0x08002b74   0x00000028   Code   RO         2382    i.HAL_Delay         stm32f1xx_hal.o
    0x08002b9c   0x08002b9c   0x00000014   Code   RO         3096    i.HAL_FLASH_Lock    stm32f1xx_hal_flash.o
    0x08002bb0   0x08002bb0   0x0000002c   Code   RO         3103    i.HAL_FLASH_Unlock  stm32f1xx_hal_flash.o
    0x08002bdc   0x08002bdc   0x000001fc   Code   RO         2695    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x08002dd8   0x08002dd8   0x00000010   Code   RO         2698    i.HAL_GPIO_TogglePin  stm32f1xx_hal_gpio.o
    0x08002de8   0x08002de8   0x0000000e   Code   RO         2699    i.HAL_GPIO_WritePin  stm32f1xx_hal_gpio.o
    0x08002df6   0x08002df6   0x00000002   PAD
    0x08002df8   0x08002df8   0x0000000c   Code   RO         2386    i.HAL_GetTick       stm32f1xx_hal.o
    0x08002e04   0x08002e04   0x00000010   Code   RO         2392    i.HAL_IncTick       stm32f1xx_hal.o
    0x08002e14   0x08002e14   0x00000024   Code   RO         2393    i.HAL_Init          stm32f1xx_hal.o
    0x08002e38   0x08002e38   0x00000044   Code   RO         2394    i.HAL_InitTick      stm32f1xx_hal.o
    0x08002e7c   0x08002e7c   0x00000064   Code   RO         1837    i.HAL_MspInit       stm32f1xx_hal_msp.o
    0x08002ee0   0x08002ee0   0x0000001c   Code   RO         2855    i.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x08002efc   0x08002efc   0x00000068   Code   RO         2861    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08002f64   0x08002f64   0x00000024   Code   RO         2862    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x08002f88   0x08002f88   0x00000130   Code   RO         2658    i.HAL_RCCEx_PeriphCLKConfig  stm32f1xx_hal_rcc_ex.o
    0x080030b8   0x080030b8   0x00000180   Code   RO         2550    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x08003238   0x08003238   0x0000000c   Code   RO         2555    i.HAL_RCC_GetHCLKFreq  stm32f1xx_hal_rcc.o
    0x08003244   0x08003244   0x00000020   Code   RO         2557    i.HAL_RCC_GetPCLK1Freq  stm32f1xx_hal_rcc.o
    0x08003264   0x08003264   0x00000020   Code   RO         2558    i.HAL_RCC_GetPCLK2Freq  stm32f1xx_hal_rcc.o
    0x08003284   0x08003284   0x00000078   Code   RO         2559    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x080032fc   0x080032fc   0x0000042c   Code   RO         2562    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x08003728   0x08003728   0x000000b4   Code   RO         3616    i.HAL_SPI_Init      stm32f1xx_hal_spi.o
    0x080037dc   0x080037dc   0x00000070   Code   RO         1499    i.HAL_SPI_MspInit   spi.o
    0x0800384c   0x0800384c   0x0000001c   Code   RO         2864    i.HAL_SYSTICK_CLKSourceConfig  stm32f1xx_hal_cortex.o
    0x08003868   0x08003868   0x00000028   Code   RO         2866    i.HAL_SYSTICK_Config  stm32f1xx_hal_cortex.o
    0x08003890   0x08003890   0x00000002   Code   RO         4644    i.HAL_TIMEx_BreakCallback  stm32f1xx_hal_tim_ex.o
    0x08003892   0x08003892   0x00000002   Code   RO         4645    i.HAL_TIMEx_CommutCallback  stm32f1xx_hal_tim_ex.o
    0x08003894   0x08003894   0x00000066   Code   RO         4647    i.HAL_TIMEx_ConfigBreakDeadTime  stm32f1xx_hal_tim_ex.o
    0x080038fa   0x080038fa   0x00000072   Code   RO         4663    i.HAL_TIMEx_MasterConfigSynchronization  stm32f1xx_hal_tim_ex.o
    0x0800396c   0x0800396c   0x000000aa   Code   RO         4674    i.HAL_TIMEx_PWMN_Start  stm32f1xx_hal_tim_ex.o
    0x08003a16   0x08003a16   0x0000006a   Code   RO         4677    i.HAL_TIMEx_PWMN_Stop  stm32f1xx_hal_tim_ex.o
    0x08003a80   0x08003a80   0x0000005c   Code   RO         3929    i.HAL_TIM_Base_Init  stm32f1xx_hal_tim.o
    0x08003adc   0x08003adc   0x00000064   Code   RO         1541    i.HAL_TIM_Base_MspInit  tim.o
    0x08003b40   0x08003b40   0x00000062   Code   RO         3934    i.HAL_TIM_Base_Start_IT  stm32f1xx_hal_tim.o
    0x08003ba2   0x08003ba2   0x000000e4   Code   RO         3938    i.HAL_TIM_ConfigClockSource  stm32f1xx_hal_tim.o
    0x08003c86   0x08003c86   0x00000002   Code   RO         3963    i.HAL_TIM_IC_CaptureCallback  stm32f1xx_hal_tim.o
    0x08003c88   0x08003c88   0x00000174   Code   RO         3977    i.HAL_TIM_IRQHandler  stm32f1xx_hal_tim.o
    0x08003dfc   0x08003dfc   0x00000074   Code   RO         1542    i.HAL_TIM_MspPostInit  tim.o
    0x08003e70   0x08003e70   0x0000004c   Code   RO         3978    i.HAL_TIM_OC_ConfigChannel  stm32f1xx_hal_tim.o
    0x08003ebc   0x08003ebc   0x00000002   Code   RO         3980    i.HAL_TIM_OC_DelayElapsedCallback  stm32f1xx_hal_tim.o
    0x08003ebe   0x08003ebe   0x0000005c   Code   RO         3982    i.HAL_TIM_OC_Init   stm32f1xx_hal_tim.o
    0x08003f1a   0x08003f1a   0x00000002   Code   RO         3984    i.HAL_TIM_OC_MspInit  stm32f1xx_hal_tim.o
    0x08003f1c   0x08003f1c   0x000000d2   Code   RO         4001    i.HAL_TIM_PWM_ConfigChannel  stm32f1xx_hal_tim.o
    0x08003fee   0x08003fee   0x0000005c   Code   RO         4004    i.HAL_TIM_PWM_Init  stm32f1xx_hal_tim.o
    0x0800404a   0x0800404a   0x00000002   Code   RO         4006    i.HAL_TIM_PWM_MspInit  stm32f1xx_hal_tim.o
    0x0800404c   0x0800404c   0x00000002   Code   RO         4007    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f1xx_hal_tim.o
    0x0800404e   0x0800404e   0x00000002   PAD
    0x08004050   0x08004050   0x000000b0   Code   RO         4009    i.HAL_TIM_PWM_Start  stm32f1xx_hal_tim.o
    0x08004100   0x08004100   0x000000ec   Code   RO         4011    i.HAL_TIM_PWM_Start_IT  stm32f1xx_hal_tim.o
    0x080041ec   0x080041ec   0x00000072   Code   RO         4012    i.HAL_TIM_PWM_Stop  stm32f1xx_hal_tim.o
    0x0800425e   0x0800425e   0x00000002   Code   RO         4015    i.HAL_TIM_PeriodElapsedCallback  stm32f1xx_hal_tim.o
    0x08004260   0x08004260   0x00000002   Code   RO         4020    i.HAL_TIM_TriggerCallback  stm32f1xx_hal_tim.o
    0x08004262   0x08004262   0x00000002   Code   RO         4922    i.HAL_UARTEx_RxEventCallback  stm32f1xx_hal_uart.o
    0x08004264   0x08004264   0x00000058   Code   RO         4934    i.HAL_UART_DMAStop  stm32f1xx_hal_uart.o
    0x080042bc   0x080042bc   0x00000002   Code   RO         4936    i.HAL_UART_ErrorCallback  stm32f1xx_hal_uart.o
    0x080042be   0x080042be   0x00000002   PAD
    0x080042c0   0x080042c0   0x00000200   Code   RO         4939    i.HAL_UART_IRQHandler  stm32f1xx_hal_uart.o
    0x080044c0   0x080044c0   0x00000064   Code   RO         4940    i.HAL_UART_Init     stm32f1xx_hal_uart.o
    0x08004524   0x08004524   0x0000022c   Code   RO         1601    i.HAL_UART_MspInit  usart.o
    0x08004750   0x08004750   0x0000002e   Code   RO         4944    i.HAL_UART_Receive_DMA  stm32f1xx_hal_uart.o
    0x0800477e   0x0800477e   0x0000002e   Code   RO         4945    i.HAL_UART_Receive_IT  stm32f1xx_hal_uart.o
    0x080047ac   0x080047ac   0x000001a8   Code   RO          549    i.HAL_UART_RxCpltCallback  modbus.o
    0x08004954   0x08004954   0x00000002   Code   RO         4947    i.HAL_UART_RxHalfCpltCallback  stm32f1xx_hal_uart.o
    0x08004956   0x08004956   0x000000ca   Code   RO         4948    i.HAL_UART_Transmit  stm32f1xx_hal_uart.o
    0x08004a20   0x08004a20   0x00000098   Code   RO         4949    i.HAL_UART_Transmit_DMA  stm32f1xx_hal_uart.o
    0x08004ab8   0x08004ab8   0x00000040   Code   RO          550    i.HAL_UART_TxCpltCallback  modbus.o
    0x08004af8   0x08004af8   0x00000002   Code   RO         4952    i.HAL_UART_TxHalfCpltCallback  stm32f1xx_hal_uart.o
    0x08004afa   0x08004afa   0x00000002   Code   RO         1662    i.HardFault_Handler  stm32f1xx_it.o
    0x08004afc   0x08004afc   0x00000030   Code   RO          247    i.IIt_DC_filter     current_loop.o
    0x08004b2c   0x08004b2c   0x00000030   Code   RO          248    i.IIt_filter        current_loop.o
    0x08004b5c   0x08004b5c   0x00000168   Code   RO          831    i.Init_Control_Parameter  parameter.o
    0x08004cc4   0x08004cc4   0x00000028   Code   RO          832    i.Init_Driver_State  parameter.o
    0x08004cec   0x08004cec   0x000003d8   Code   RO          551    i.Init_Modbus_Addr_List  modbus.o
    0x080050c4   0x080050c4   0x00000070   Code   RO          833    i.Init_Motor_Parameter  parameter.o
    0x08005134   0x08005134   0x00000090   Code   RO          834    i.Init_System_Parameter  parameter.o
    0x080051c4   0x080051c4   0x0000001c   Code   RO          249    i.Init_current_loop_state  current_loop.o
    0x080051e0   0x080051e0   0x0000000c   Code   RO         1080    i.Init_position_loop_state  position_loop.o
    0x080051ec   0x080051ec   0x00000014   Code   RO         1212    i.Init_velocity_loop_state  velocity_loop.o
    0x08005200   0x08005200   0x000000cc   Code   RO          712    i.LED_Process       motion_control.o
    0x080052cc   0x080052cc   0x00000010   Code   RO         1214    i.Low_pass_filter_1  velocity_loop.o
    0x080052dc   0x080052dc   0x00000080   Code   RO         1386    i.MX_ADC1_Init      adc.o
    0x0800535c   0x0800535c   0x00000080   Code   RO         1387    i.MX_ADC2_Init      adc.o
    0x080053dc   0x080053dc   0x00000044   Code   RO         1434    i.MX_CAN_Init       can.o
    0x08005420   0x08005420   0x0000006c   Code   RO         1474    i.MX_DMA_Init       dma.o
    0x0800548c   0x0800548c   0x000000f4   Code   RO         1360    i.MX_GPIO_Init      gpio.o
    0x08005580   0x08005580   0x0000004c   Code   RO         1500    i.MX_SPI1_Init      spi.o
    0x080055cc   0x080055cc   0x00000110   Code   RO         1543    i.MX_TIM1_Init      tim.o
    0x080056dc   0x080056dc   0x00000068   Code   RO         1544    i.MX_TIM4_Init      tim.o
    0x08005744   0x08005744   0x0000003c   Code   RO         1602    i.MX_USART1_UART_Init  usart.o
    0x08005780   0x08005780   0x00000038   Code   RO         1603    i.MX_USART2_UART_Init  usart.o
    0x080057b8   0x080057b8   0x00000038   Code   RO         1604    i.MX_USART3_UART_Init  usart.o
    0x080057f0   0x080057f0   0x00000002   Code   RO         1663    i.MemManage_Handler  stm32f1xx_it.o
    0x080057f2   0x080057f2   0x00000002   PAD
    0x080057f4   0x080057f4   0x00000038   Code   RO          836    i.MemReadModbus     parameter.o
    0x0800582c   0x0800582c   0x000000a4   Code   RO          552    i.Modbus_03_Solve   modbus.o
    0x080058d0   0x080058d0   0x00000070   Code   RO          553    i.Modbus_06_Solve   modbus.o
    0x08005940   0x08005940   0x000000b8   Code   RO          554    i.Modbus_16_Solve   modbus.o
    0x080059f8   0x080059f8   0x00000010   Code   RO          555    i.Modbus_Solve_485_Disenable  modbus.o
    0x08005a08   0x08005a08   0x00000010   Code   RO          556    i.Modbus_Solve_485_Enable  modbus.o
    0x08005a18   0x08005a18   0x000000a4   Code   RO          558    i.Modbus_Solve_Service  modbus.o
    0x08005abc   0x08005abc   0x00000368   Code   RO          713    i.Motion_process    motion_control.o
    0x08005e24   0x08005e24   0x00000002   Code   RO         1664    i.NMI_Handler       stm32f1xx_it.o
    0x08005e26   0x08005e26   0x00000024   Code   RO         1545    i.OC4_PWM_Override  tim.o
    0x08005e4a   0x08005e4a   0x00000002   Code   RO         1665    i.PendSV_Handler    stm32f1xx_it.o
    0x08005e4c   0x08005e4c   0x00000128   Code   RO         1081    i.Position_Loop     position_loop.o
    0x08005f74   0x08005f74   0x00000174   Code   RO           15    i.Process_RPDO      canopen.o
    0x080060e8   0x080060e8   0x00000084   Code   RO           16    i.Process_TPDO      canopen.o
    0x0800616c   0x0800616c   0x00000024   Code   RO         2563    i.RCC_Delay         stm32f1xx_hal_rcc.o
    0x08006190   0x08006190   0x00000002   Code   RO         1666    i.RCC_IRQHandler    stm32f1xx_it.o
    0x08006192   0x08006192   0x00000002   PAD
    0x08006194   0x08006194   0x00000058   Code   RO         1273    i.ReadValue         mt6825.o
    0x080061ec   0x080061ec   0x00000038   Code   RO          383    i.Read_OD           dic.o
    0x08006224   0x08006224   0x00000160   Code   RO           17    i.SDO_Process       canopen.o
    0x08006384   0x08006384   0x00000038   Code   RO         1274    i.SPIx_ReadWriteByte  mt6825.o
    0x080063bc   0x080063bc   0x00000002   Code   RO         1667    i.SVC_Handler       stm32f1xx_it.o
    0x080063be   0x080063be   0x0000014a   Code   RO         1197    i.SVM               utils.o
    0x08006508   0x08006508   0x00000034   Code   RO          387    i.Search_OD         dic.o
    0x0800653c   0x0800653c   0x0000000e   Code   RO         1668    i.SysTick_Handler   stm32f1xx_it.o
    0x0800654a   0x0800654a   0x00000060   Code   RO         1316    i.SystemClock_Config  main.o
    0x080065aa   0x080065aa   0x00000002   Code   RO         5279    i.SystemInit        system_stm32f1xx.o
    0x080065ac   0x080065ac   0x000001ec   Code   RO         1669    i.TIM1_CC_IRQHandler  stm32f1xx_it.o
    0x08006798   0x08006798   0x00000050   Code   RO         1670    i.TIM4_IRQHandler   stm32f1xx_it.o
    0x080067e8   0x080067e8   0x00000078   Code   RO         4022    i.TIM_Base_SetConfig  stm32f1xx_hal_tim.o
    0x08006860   0x08006860   0x0000001e   Code   RO         4023    i.TIM_CCxChannelCmd  stm32f1xx_hal_tim.o
    0x0800687e   0x0800687e   0x0000001e   Code   RO         4683    i.TIM_CCxNChannelCmd  stm32f1xx_hal_tim_ex.o
    0x0800689c   0x0800689c   0x00000016   Code   RO         4033    i.TIM_ETR_SetConfig  stm32f1xx_hal_tim.o
    0x080068b2   0x080068b2   0x00000010   Code   RO         4034    i.TIM_ITRx_SetConfig  stm32f1xx_hal_tim.o
    0x080068c2   0x080068c2   0x00000002   PAD
    0x080068c4   0x080068c4   0x00000068   Code   RO         4035    i.TIM_OC1_SetConfig  stm32f1xx_hal_tim.o
    0x0800692c   0x0800692c   0x00000068   Code   RO         4036    i.TIM_OC2_SetConfig  stm32f1xx_hal_tim.o
    0x08006994   0x08006994   0x00000068   Code   RO         4037    i.TIM_OC3_SetConfig  stm32f1xx_hal_tim.o
    0x080069fc   0x080069fc   0x0000004e   Code   RO         4038    i.TIM_OC4_SetConfig  stm32f1xx_hal_tim.o
    0x08006a4a   0x08006a4a   0x00000026   Code   RO         4040    i.TIM_TI1_ConfigInputStage  stm32f1xx_hal_tim.o
    0x08006a70   0x08006a70   0x00000024   Code   RO         4042    i.TIM_TI2_ConfigInputStage  stm32f1xx_hal_tim.o
    0x08006a94   0x08006a94   0x00000090   Code   RO         1861    i.Tamagawa_Read_Cmd  tamagawa.o
    0x08006b24   0x08006b24   0x00000010   Code   RO         4953    i.UART_DMAAbortOnError  stm32f1xx_hal_uart.o
    0x08006b34   0x08006b34   0x0000004a   Code   RO         4954    i.UART_DMAError     stm32f1xx_hal_uart.o
    0x08006b7e   0x08006b7e   0x0000005c   Code   RO         4955    i.UART_DMAReceiveCplt  stm32f1xx_hal_uart.o
    0x08006bda   0x08006bda   0x0000001a   Code   RO         4957    i.UART_DMARxHalfCplt  stm32f1xx_hal_uart.o
    0x08006bf4   0x08006bf4   0x00000030   Code   RO         4959    i.UART_DMATransmitCplt  stm32f1xx_hal_uart.o
    0x08006c24   0x08006c24   0x0000000a   Code   RO         4961    i.UART_DMATxHalfCplt  stm32f1xx_hal_uart.o
    0x08006c2e   0x08006c2e   0x00000030   Code   RO         4963    i.UART_EndRxTransfer  stm32f1xx_hal_uart.o
    0x08006c5e   0x08006c5e   0x0000001a   Code   RO         4964    i.UART_EndTransmit_IT  stm32f1xx_hal_uart.o
    0x08006c78   0x08006c78   0x00000012   Code   RO         4965    i.UART_EndTxTransfer  stm32f1xx_hal_uart.o
    0x08006c8a   0x08006c8a   0x000000be   Code   RO         4966    i.UART_Receive_IT   stm32f1xx_hal_uart.o
    0x08006d48   0x08006d48   0x000000c8   Code   RO         4967    i.UART_SetConfig    stm32f1xx_hal_uart.o
    0x08006e10   0x08006e10   0x00000074   Code   RO         4968    i.UART_Start_Receive_DMA  stm32f1xx_hal_uart.o
    0x08006e84   0x08006e84   0x00000036   Code   RO         4969    i.UART_Start_Receive_IT  stm32f1xx_hal_uart.o
    0x08006eba   0x08006eba   0x0000005e   Code   RO         4970    i.UART_Transmit_IT  stm32f1xx_hal_uart.o
    0x08006f18   0x08006f18   0x00000078   Code   RO         4971    i.UART_WaitOnFlagUntilTimeout  stm32f1xx_hal_uart.o
    0x08006f90   0x08006f90   0x0000000c   Code   RO         1671    i.USART1_IRQHandler  stm32f1xx_it.o
    0x08006f9c   0x08006f9c   0x000000a4   Code   RO         1672    i.USART2_IRQHandler  stm32f1xx_it.o
    0x08007040   0x08007040   0x00000084   Code   RO         1673    i.USART3_IRQHandler  stm32f1xx_it.o
    0x080070c4   0x080070c4   0x0000000c   Code   RO         1674    i.USB_LP_CAN1_RX0_IRQHandler  stm32f1xx_it.o
    0x080070d0   0x080070d0   0x000000a0   Code   RO         1215    i.Update_Speed      velocity_loop.o
    0x08007170   0x08007170   0x00000002   Code   RO         1675    i.UsageFault_Handler  stm32f1xx_it.o
    0x08007172   0x08007172   0x00000002   PAD
    0x08007174   0x08007174   0x00000150   Code   RO         1216    i.Velocity_loop     velocity_loop.o
    0x080072c4   0x080072c4   0x00000038   Code   RO          391    i.Write_OD          dic.o
    0x080072fc   0x080072fc   0x00000020   Code   RO         5571    i.__0printf         mc_w.l(printfa.o)
    0x0800731c   0x0800731c   0x0000000c   Code   RO         5642    i.__read_errno      mc_w.l(errno.o)
    0x08007328   0x08007328   0x0000000e   Code   RO         5688    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08007336   0x08007336   0x00000002   Code   RO         5689    i.__scatterload_null  mc_w.l(handlers.o)
    0x08007338   0x08007338   0x0000000e   Code   RO         5690    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08007346   0x08007346   0x00000002   PAD
    0x08007348   0x08007348   0x0000000c   Code   RO         5643    i.__set_errno       mc_w.l(errno.o)
    0x08007354   0x08007354   0x00000184   Code   RO         5578    i._fp_digits        mc_w.l(printfa.o)
    0x080074d8   0x080074d8   0x0000000e   Code   RO         5677    i._is_digit         mc_w.l(scanf_fp.o)
    0x080074e6   0x080074e6   0x00000002   PAD
    0x080074e8   0x080074e8   0x000006b4   Code   RO         5579    i._printf_core      mc_w.l(printfa.o)
    0x08007b9c   0x08007b9c   0x00000024   Code   RO         5580    i._printf_post_padding  mc_w.l(printfa.o)
    0x08007bc0   0x08007bc0   0x0000002e   Code   RO         5581    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08007bee   0x08007bee   0x00000002   PAD
    0x08007bf0   0x08007bf0   0x0000003c   Code   RO         1138    i.arm_cos_f32       sin_table.o
    0x08007c2c   0x08007c2c   0x00000038   Code   RO         1139    i.arm_sin_f32       sin_table.o
    0x08007c64   0x08007c64   0x0000002a   Code   RO         5314    i.atof              m_ws.l(atof.o)
    0x08007c8e   0x08007c8e   0x00000002   PAD
    0x08007c90   0x08007c90   0x00000014   Code   RO          337    i.delay_init        delay.o
    0x08007ca4   0x08007ca4   0x00000004   Code   RO          338    i.delay_ms          delay.o
    0x08007ca8   0x08007ca8   0x00000002   Code   RO          393    i.fdummy            dic.o
    0x08007caa   0x08007caa   0x00000002   PAD
    0x08007cac   0x08007cac   0x000001c4   Code   RO          912    i.find_commutation  mcpwm.o
    0x08007e70   0x08007e70   0x00000018   Code   RO         1933    i.fputc             vofa_function.o
    0x08007e88   0x08007e88   0x00000024   Code   RO          913    i.get_electric_phase  mcpwm.o
    0x08007eac   0x08007eac   0x00000078   Code   RO          914    i.init_motor_control  mcpwm.o
    0x08007f24   0x08007f24   0x0000012c   Code   RO         1317    i.main              main.o
    0x08008050   0x08008050   0x00000020   Code   RO          915    i.phase_current_from_adcval  mcpwm.o
    0x08008070   0x08008070   0x0000004c   Code   RO          916    i.queue_modulation_timings  mcpwm.o
    0x080080bc   0x080080bc   0x0000004c   Code   RO         5318    i.sqrt              m_ws.l(sqrt.o)
    0x08008108   0x08008108   0x00000070   Code   RO          918    i.start_adc         mcpwm.o
    0x08008178   0x08008178   0x00000048   Code   RO          919    i.start_pwm         mcpwm.o
    0x080081c0   0x080081c0   0x00000048   Code   RO          920    i.stop_pwm          mcpwm.o
    0x08008208   0x08008208   0x00000038   Code   RO         1862    i.uart1_init        tamagawa.o
    0x08008240   0x08008240   0x00000054   Code   RO          567    i.uart2_init        modbus.o
    0x08008294   0x08008294   0x00000044   Code   RO          568    i.uart3_init        modbus.o
    0x080082d8   0x080082d8   0x00000040   Code   RO         1934    i.uartCMDRecv       vofa_function.o
    0x08008318   0x08008318   0x000000e4   Code   RO          921    i.update_motor      mcpwm.o
    0x080083fc   0x080083fc   0x00000190   Code   RO         1938    i.vofaCommandParse  vofa_function.o
    0x0800858c   0x0800858c   0x00000020   Code   RO         1939    i.vofaJustFloatInit  vofa_function.o
    0x080085ac   0x080085ac   0x00000200   Data   RO          217    .constdata          crc_16.o
    0x080087ac   0x080087ac   0x0000040c   Data   RO          397    .constdata          dic.o
    0x08008bb8   0x08008bb8   0x00001000   Data   RO         1140    .constdata          sin_table.o
    0x08009bb8   0x08009bb8   0x00000010   Data   RO         5280    .constdata          system_stm32f1xx.o
    0x08009bc8   0x08009bc8   0x00000008   Data   RO         5281    .constdata          system_stm32f1xx.o
    0x08009bd0   0x08009bd0   0x00000040   Data   RO         5682    .constdata          mc_w.l(ctype_c.o)
    0x08009c10   0x08009c10   0x00000020   Data   RO         5686    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08009c30, Size: 0x000019f8, Max: 0x00005000, ABSOLUTE, COMPRESSED[0x00000104])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x0000001a   Data   RW           19    .data               canopen.o
    0x2000001a   COMPRESSED   0x00000001   Data   RW           20    .data               canopen.o
    0x2000001b   COMPRESSED   0x00000001   PAD
    0x2000001c   COMPRESSED   0x00000002   Data   RW           21    .data               canopen.o
    0x2000001e   COMPRESSED   0x00000002   Data   RW           22    .data               canopen.o
    0x20000020   COMPRESSED   0x0000006c   Data   RW          250    .data               current_loop.o
    0x2000008c   COMPRESSED   0x00000002   Data   RW          251    .data               current_loop.o
    0x2000008e   COMPRESSED   0x00000002   PAD
    0x20000090   COMPRESSED   0x00000004   Data   RW          252    .data               current_loop.o
    0x20000094   COMPRESSED   0x00000004   Data   RW          253    .data               current_loop.o
    0x20000098   COMPRESSED   0x00000004   Data   RW          263    .data               current_loop.o
    0x2000009c   COMPRESSED   0x00000004   Data   RW          264    .data               current_loop.o
    0x200000a0   COMPRESSED   0x00000002   Data   RW          265    .data               current_loop.o
    0x200000a2   COMPRESSED   0x00000002   Data   RW          266    .data               current_loop.o
    0x200000a4   COMPRESSED   0x00000004   Data   RW          340    .data               delay.o
    0x200000a8   COMPRESSED   0x00000004   Data   RW          398    .data               dic.o
    0x200000ac   COMPRESSED   0x00000068   Data   RW          399    .data               dic.o
    0x20000114   COMPRESSED   0x00000008   Data   RW          517    .data               ds402.o
    0x2000011c   COMPRESSED   0x00000002   Data   RW          519    .data               ds402.o
    0x2000011e   COMPRESSED   0x00000002   PAD
    0x20000120   COMPRESSED   0x00000028   Data   RW          570    .data               modbus.o
    0x20000148   COMPRESSED   0x00000004   Data   RW          571    .data               modbus.o
    0x2000014c   COMPRESSED   0x00000004   Data   RW          573    .data               modbus.o
    0x20000150   COMPRESSED   0x00000060   Data   RW          720    .data               motion_control.o
    0x200001b0   COMPRESSED   0x00000004   Data   RW          721    .data               motion_control.o
    0x200001b4   COMPRESSED   0x00000004   Data   RW          722    .data               motion_control.o
    0x200001b8   COMPRESSED   0x00000004   Data   RW          723    .data               motion_control.o
    0x200001bc   COMPRESSED   0x00000004   Data   RW          724    .data               motion_control.o
    0x200001c0   COMPRESSED   0x00000001   Data   RW          725    .data               motion_control.o
    0x200001c1   COMPRESSED   0x00000003   PAD
    0x200001c4   COMPRESSED   0x00000004   Data   RW          730    .data               motion_control.o
    0x200001c8   COMPRESSED   0x00000004   Data   RW          731    .data               motion_control.o
    0x200001cc   COMPRESSED   0x00000002   Data   RW          737    .data               motion_control.o
    0x200001ce   COMPRESSED   0x00000002   PAD
    0x200001d0   COMPRESSED   0x00000004   Data   RW          750    .data               motion_control.o
    0x200001d4   COMPRESSED   0x00000054   Data   RW          800    .data               ntc_calculate.o
    0x20000228   COMPRESSED   0x00000004   Data   RW          801    .data               ntc_calculate.o
    0x2000022c   COMPRESSED   0x00000004   Data   RW          840    .data               parameter.o
    0x20000230   COMPRESSED   0x00000002   Data   RW          926    .data               mcpwm.o
    0x20000232   COMPRESSED   0x00000002   Data   RW          927    .data               mcpwm.o
    0x20000234   COMPRESSED   0x0000008c   Data   RW          928    .data               mcpwm.o
    0x200002c0   COMPRESSED   0x00000002   Data   RW          930    .data               mcpwm.o
    0x200002c2   COMPRESSED   0x00000002   PAD
    0x200002c4   COMPRESSED   0x00000004   Data   RW          931    .data               mcpwm.o
    0x200002c8   COMPRESSED   0x00000004   Data   RW          932    .data               mcpwm.o
    0x200002cc   COMPRESSED   0x00000004   Data   RW          933    .data               mcpwm.o
    0x200002d0   COMPRESSED   0x00000004   Data   RW          934    .data               mcpwm.o
    0x200002d4   COMPRESSED   0x00000004   Data   RW          935    .data               mcpwm.o
    0x200002d8   COMPRESSED   0x00000004   Data   RW          936    .data               mcpwm.o
    0x200002dc   COMPRESSED   0x00000004   Data   RW          938    .data               mcpwm.o
    0x200002e0   COMPRESSED   0x00000004   Data   RW          939    .data               mcpwm.o
    0x200002e4   COMPRESSED   0x00000004   Data   RW          940    .data               mcpwm.o
    0x200002e8   COMPRESSED   0x00000004   Data   RW          941    .data               mcpwm.o
    0x200002ec   COMPRESSED   0x00000004   Data   RW          942    .data               mcpwm.o
    0x200002f0   COMPRESSED   0x00000004   Data   RW          943    .data               mcpwm.o
    0x200002f4   COMPRESSED   0x00000004   Data   RW          944    .data               mcpwm.o
    0x200002f8   COMPRESSED   0x00000004   Data   RW          945    .data               mcpwm.o
    0x200002fc   COMPRESSED   0x00000004   Data   RW          946    .data               mcpwm.o
    0x20000300   COMPRESSED   0x00000002   Data   RW          947    .data               mcpwm.o
    0x20000302   COMPRESSED   0x00000002   PAD
    0x20000304   COMPRESSED   0x00000004   Data   RW          948    .data               mcpwm.o
    0x20000308   COMPRESSED   0x00000002   Data   RW          951    .data               mcpwm.o
    0x2000030a   COMPRESSED   0x00000002   Data   RW          966    .data               mcpwm.o
    0x2000030c   COMPRESSED   0x00000002   Data   RW          971    .data               mcpwm.o
    0x2000030e   COMPRESSED   0x00000002   PAD
    0x20000310   COMPRESSED   0x00000004   Data   RW          982    .data               mcpwm.o
    0x20000314   COMPRESSED   0x00000004   Data   RW          983    .data               mcpwm.o
    0x20000318   COMPRESSED   0x00000002   Data   RW          984    .data               mcpwm.o
    0x2000031a   COMPRESSED   0x00000002   PAD
    0x2000031c   COMPRESSED   0x00000004   Data   RW          985    .data               mcpwm.o
    0x20000320   COMPRESSED   0x00000004   Data   RW          986    .data               mcpwm.o
    0x20000324   COMPRESSED   0x00000004   Data   RW          987    .data               mcpwm.o
    0x20000328   COMPRESSED   0x00000004   Data   RW          988    .data               mcpwm.o
    0x2000032c   COMPRESSED   0x00000004   Data   RW          989    .data               mcpwm.o
    0x20000330   COMPRESSED   0x00000002   Data   RW          991    .data               mcpwm.o
    0x20000332   COMPRESSED   0x00000002   Data   RW          992    .data               mcpwm.o
    0x20000334   COMPRESSED   0x00000002   Data   RW          993    .data               mcpwm.o
    0x20000336   COMPRESSED   0x00000002   Data   RW          994    .data               mcpwm.o
    0x20000338   COMPRESSED   0x00000030   Data   RW         1082    .data               position_loop.o
    0x20000368   COMPRESSED   0x00000002   Data   RW         1083    .data               position_loop.o
    0x2000036a   COMPRESSED   0x00000002   Data   RW         1141    .data               sin_table.o
    0x2000036c   COMPRESSED   0x00000050   Data   RW         1218    .data               velocity_loop.o
    0x200003bc   COMPRESSED   0x00000002   Data   RW         1219    .data               velocity_loop.o
    0x200003be   COMPRESSED   0x0000000c   Data   RW         1275    .data               mt6825.o
    0x200003ca   COMPRESSED   0x00000002   PAD
    0x200003cc   COMPRESSED   0x00000004   Data   RW         1684    .data               stm32f1xx_it.o
    0x200003d0   COMPRESSED   0x00000006   Data   RW         1866    .data               tamagawa.o
    0x200003d6   COMPRESSED   0x00000002   PAD
    0x200003d8   COMPRESSED   0x00000004   Data   RW         1869    .data               tamagawa.o
    0x200003dc   COMPRESSED   0x00000002   Data   RW         1871    .data               tamagawa.o
    0x200003de   COMPRESSED   0x00000002   Data   RW         1872    .data               tamagawa.o
    0x200003e0   COMPRESSED   0x00000002   Data   RW         1873    .data               tamagawa.o
    0x200003e2   COMPRESSED   0x00000002   Data   RW         1874    .data               tamagawa.o
    0x200003e4   COMPRESSED   0x00000002   Data   RW         1879    .data               tamagawa.o
    0x200003e6   COMPRESSED   0x00000002   Data   RW         1880    .data               tamagawa.o
    0x200003e8   COMPRESSED   0x00000002   Data   RW         1883    .data               tamagawa.o
    0x200003ea   COMPRESSED   0x00000002   Data   RW         1884    .data               tamagawa.o
    0x200003ec   COMPRESSED   0x00000004   Data   RW         1885    .data               tamagawa.o
    0x200003f0   COMPRESSED   0x00000004   Data   RW         1886    .data               tamagawa.o
    0x200003f4   COMPRESSED   0x00000004   Data   RW         1887    .data               tamagawa.o
    0x200003f8   COMPRESSED   0x00000002   Data   RW         1888    .data               tamagawa.o
    0x200003fa   COMPRESSED   0x00000002   Data   RW         1892    .data               tamagawa.o
    0x200003fc   COMPRESSED   0x00000002   Data   RW         1898    .data               tamagawa.o
    0x200003fe   COMPRESSED   0x00000001   Data   RW         1948    .data               vofa_function.o
    0x200003ff   COMPRESSED   0x00000001   PAD
    0x20000400   COMPRESSED   0x0000000c   Data   RW         2400    .data               stm32f1xx_hal.o
    0x2000040c   COMPRESSED   0x00000004   Data   RW         5282    .data               system_stm32f1xx.o
    0x20000410   COMPRESSED   0x00000004   Data   RW         5644    .data               mc_w.l(errno.o)
    0x20000414        -       0x00000068   Zero   RW           18    .bss                canopen.o
    0x2000047c        -       0x00000af0   Zero   RW          569    .bss                modbus.o
    0x20000f6c        -       0x00000030   Zero   RW          923    .bss                mcpwm.o
    0x20000f9c        -       0x00000060   Zero   RW         1388    .bss                adc.o
    0x20000ffc        -       0x00000028   Zero   RW         1435    .bss                can.o
    0x20001024        -       0x00000058   Zero   RW         1501    .bss                spi.o
    0x2000107c        -       0x00000090   Zero   RW         1546    .bss                tim.o
    0x2000110c        -       0x00000220   Zero   RW         1605    .bss                usart.o
    0x2000132c        -       0x000000c8   Zero   RW         1863    .bss                tamagawa.o
    0x200013f4        -       0x000000c8   Zero   RW         1864    .bss                tamagawa.o
    0x200014bc        -       0x0000013c   Zero   RW         1947    .bss                vofa_function.o
    0x200015f8        -       0x00000400   Zero   RW            1    STACK               startup_stm32f103xb.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       456         38          0          0         96       2490   adc.o
       192         20          0          0         40       1619   can.o
      1052        340          0         31        104     471382   canopen.o
        44          4        512          0          0       1258   crc_16.o
       728         64          0        130          0      13282   current_loop.o
        24          4          0          4          0       1088   delay.o
       166         16       1036        108          0       4178   dic.o
       108          4          0          0          0        770   dma.o
       268         70          0         10          0       1094   ds402.o
       244         18          0          0          0        979   gpio.o
       398         68          0          0          0       6164   main.o
      1368        134          0        256         48      16621   mcpwm.o
      2280        498          0         48       2800       9799   modbus.o
      1144         84          0        127          0       6094   motion_control.o
       144         16          0         12          0       1751   mt6825.o
        84          4          0         88          0       1298   ntc_calculate.o
       956        328          0          4          0       3972   parameter.o
       588        110          0         50          0      12525   position_loop.o
       116         16       4096          2          0       1536   sin_table.o
       188         22          0          0         88       1625   spi.o
        36          8        236          0       1024        792   startup_stm32f103xb.o
       172         30          0         12          0       5829   stm32f1xx_hal.o
      1076         24          0          0          0       6447   stm32f1xx_hal_adc.o
       808         16          0          0          0       3838   stm32f1xx_hal_adc_ex.o
      1972          0          0          0          0      14175   stm32f1xx_hal_can.o
       236         16          0          0          0      29249   stm32f1xx_hal_cortex.o
       916         72          0          0          0       5050   stm32f1xx_hal_dma.o
        64         18          0          0          0       1092   stm32f1xx_hal_flash.o
       538         12          0          0          0       3425   stm32f1xx_hal_gpio.o
       100         10          0          0          0        866   stm32f1xx_hal_msp.o
      1684         98          0          0          0       6800   stm32f1xx_hal_rcc.o
       304         14          0          0          0       1444   stm32f1xx_hal_rcc_ex.o
       180          0          0          0          0       1127   stm32f1xx_hal_spi.o
      2452         40          0          0          0      19744   stm32f1xx_hal_tim.o
       526          0          0          0          0       5454   stm32f1xx_hal_tim_ex.o
      2286         40          0          0          0      18669   stm32f1xx_hal_uart.o
      1012        242          0          4          0      11644   stm32f1xx_it.o
         2          0         24          4          0       1091   system_stm32f1xx.o
       200         36          0         44        400       3857   tamagawa.o
       628         48          0          0        144       3664   tim.o
       728         72          0          0        544       3463   usart.o
       330          0          0          0          0       1194   utils.o
       532         76          0         82          0      12500   velocity_loop.o
       520         74          0          1        316       7080   vofa_function.o

    ----------------------------------------------------------------------
     27876       <USER>       <GROUP>       1040       5604     728019   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        26          0          0         23          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        42          0          0          0          0         84   atof.o
        76          0          0          0          0         84   sqrt.o
        86          0          0          0          0          0   __dczerorl2.o
        64          0          0          0          0         84   _sgetc.o
        40          6         64          0          0         68   ctype_c.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        24         12          0          4          0        136   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        10          0          0          0          0         68   isspace_c.o
         0          0          0          0          0          0   iusefp.o
        98          0          0          0          0         84   ldiv.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2218         90          0          0          0        464   printfa.o
       878         12          0          0          0        216   scanf_fp.o
       156         12          0          0          0        120   strtod.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        74          0          0          0          0         80   dfixl.o
        48          0          0          0          0         68   dfixul.o
        40          0          0          0          0         80   dfltl.o
        24          0          0          0          0         76   dfltul.o
       228          0          0          0          0         96   dmul.o
       162          0          0          0          0        100   dsqrt.o
       176          0          0          0          0        140   fadd.o
       124          0          0          0          0         88   fdiv.o
       110          0          0          0          0        168   fepilogue.o
        50          0          0          0          0         68   ffixi.o
        18          0          0          0          0         68   fflti.o
       100          0          0          0          0         76   fmul.o

    ----------------------------------------------------------------------
      6108        <USER>         <GROUP>          4          0       3716   Library Totals
        14          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       118          0          0          0          0        168   m_ws.l
      3976        148         64          4          0       1860   mc_w.l
      2000          0          0          0          0       1688   mf_w.l

    ----------------------------------------------------------------------
      6108        <USER>         <GROUP>          4          0       3716   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     33984       2952       6000       1044       5604     718083   Grand Totals
     33984       2952       6000        260       5604     718083   ELF Image Totals (compressed)
     33984       2952       6000        260          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                39984 (  39.05kB)
    Total RW  Size (RW Data + ZI Data)              6648 (   6.49kB)
    Total ROM Size (Code + RO Data + RW Data)      40244 (  39.30kB)

==============================================================================

