#ifndef __CANOPEN_H
#define __CANOPEN_H

#include "can.h"


extern CAN_TxHeaderTypeDef     TxMeg;
extern CAN_RxHeaderTypeDef     RxMeg;

extern uint8_t  CAN_Rx_Data[8];
extern uint8_t  CAN_Tx_Data[8];

extern uint16_t CAN_Baudrate;
extern uint8_t Error_register;

extern uint16_t TPDO_Period,tim_count;

void CAN_User_Init(CAN_HandleTypeDef* hcan );
void CAN_TRANSMIT(void);
uint8_t CANx_SendNormalData(CAN_HandleTypeDef* hcan,uint16_t ID,uint8_t *pData,uint16_t Len);
void Init_PDO_Mapping(void);
void Process_TPDO(void);
void Process_RPDO(CAN_RxHeaderTypeDef *pHeader, uint8_t aData[]);
uint8_t SDO_Process(CAN_RxHeaderTypeDef *pHeader, uint8_t pbuf[]);

#endif
