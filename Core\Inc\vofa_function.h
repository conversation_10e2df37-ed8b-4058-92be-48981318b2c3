#ifndef VOFA_FUNCTIONS_H__
#define VOFA_FUNCTIONS_H__

#define INVALID 0xFF
#define FRAME_TAIL_SIZE (4U)
#define INCREASE_STEP (0.01f)
#define DECREASE_STEP (0.01f)
#define CH_COUNT (3U)				//开启的通道数量
#define CMD_FRAME_SIZE 15          // 增加命令帧大小以适应更长的命令

#include <stdint.h>

enum CommandID
{
	Direct_Assignment,
	Increase,
	Decrease
};

enum CommandType
{
	Speed,           // 速度命令
	Position,        // 位置命令
	Current,         // 电流命令
	MotorEnable,     // 电机使能
	CurrentLoopKp,   // 电流环Kp
	CurrentLoopKi,   // 电流环Ki
	SpeedLoopKp,     // 速度环Kp
	SpeedLoopKi,     // 速度环Ki
	PositionLoopKp,  // 位置环Kp
	PositionLoopKi   // 位置环Ki
};

typedef struct vofaJustFloatFrame
{
	float   fdata[CH_COUNT];
	uint8_t frametail[FRAME_TAIL_SIZE];
} vofaJustFloatFrame;

typedef struct vofaCommand
{
	uint8_t cmdType;
	uint8_t cmdID;
	uint8_t validData[4];
	uint8_t uartRxPacket[CMD_FRAME_SIZE]; //串口数据包接收数组
	uint8_t completionFlag;
	float   floatData;
} vofaCommand;

#ifdef __cplusplus
extern "C" {
#endif

void vofaSendJustFloat(vofaJustFloatFrame* vofaJFFrame);     //以JustFloat协议发送数据
void vofaSendFirewater(const float* fdata, uint32_t ulSize); //以Firewater协议发送数据
void vofaSendRawdata(uint8_t* pData, uint32_t ulSize);       //以rawdata协议发送数据

// uint16类型数据发送函数
void vofaSendUint16AsFloat(const uint16_t* uint16_data, uint8_t count);  // 将uint16数据以JustFloat协议发送

// int类型数据发送函数
void vofaSendIntAsFloat(const int* int_data, uint8_t count);           // 将int数据以JustFloat协议发送
// int32_t类型数据发送函数
void vofaSendInt32AsFloat(const int32_t* int32_data, uint8_t count);   // 将int32_t数据以JustFloat协议发送


// 混合类型数据发送函数
void vofaSendMixedDataAsFloat(const float* values, uint8_t count);     // 以JustFloat协议发送混合类型数据

void vofaJustFloatInit(void);        //Justfloat协议初始化
void uartCMDRecv(uint8_t byte_data); //uart串口接收单字节并存入vofaCommandData数据包
void vofaCommandParse(void);         //解析命令

// 数据转换函数
float uint8Array2Float(const uint8_t* u8Array);
void float2uint8Array(uint8_t* u8Array, const float* fdata);

extern vofaJustFloatFrame JustFloat_Data;  //包含接收到的浮点数据的结构体
extern vofaCommand        vofaCommandData; //包含命令的结构体

#ifdef __cplusplus
}
#endif

#endif
